#!/bin/bash

# Create a deployment package for remote servers

# Create a directory for the package
PACKAGE_DIR="vllm-detector-deployment"
mkdir -p $PACKAGE_DIR

# Copy the necessary files
cp docker-compose.remote.yml $PACKAGE_DIR/
cp docker-compose.remote.scanner.yml $PACKAGE_DIR/
cp remote-deploy.sh $PACKAGE_DIR/
cp AMD64_DEPLOYMENT.md $PACKAGE_DIR/README.md

# Create a zip file
zip -r $PACKAGE_DIR.zip $PACKAGE_DIR

# Clean up
rm -rf $PACKAGE_DIR

echo "Deployment package created: $PACKAGE_DIR.zip"
echo "Copy this file to your target server, unzip it, and run the remote-deploy.sh script."
