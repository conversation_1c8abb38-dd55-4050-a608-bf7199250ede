# Docker Deployment

This document provides instructions for deploying the vLLM Detector Web Application using Docker.

## Prerequisites

- Docker installed on your system
- Docker Compose (optional, but recommended)

## Quick Start

### Using Docker Compose (Recommended)

#### Web Application Only

1. Build and start the container with just the web application:

```bash
./docker-compose-up.sh
```

2. Access the web application at `http://localhost:5000`

3. To stop the container:

```bash
docker-compose down
```

#### Web Application with Scanner Service

1. Build and start the container with both the web application and scanner service:

```bash
./docker-compose-scanner-up.sh
```

2. Access the web application at `http://localhost:5000`

3. To stop the container:

```bash
docker-compose -f docker-compose.scanner.yml down
```

> **Note**: Before using the scanner service, make sure to configure the host to scan by editing the `SCAN_HOST` environment variable in `docker-compose.scanner.yml`.

### Using Docker Directly

1. Build the Docker image:

```bash
./docker-build.sh
```

2. Run the container:

```bash
./docker-run.sh
```

3. Access the web application at `http://localhost:5000`

4. To stop and remove the container:

```bash
docker stop vllm-detector
docker rm vllm-detector
```

## Configuration

### Environment Variables

You can configure the application using the following environment variables:

#### Web Application

- `SECRET_KEY`: Flask secret key (default: "vllm-detector-secret-key")
- `WEB_PORT`: Port for the web application (default: 5000)

#### Scanner Service

- `SCAN_ENABLED`: Enable or disable the scanner service (default: "false")
- `SCAN_HOST`: Host to scan for vLLM services (default: "localhost")
- `SCAN_PORT_MIN`: Minimum port to scan (default: 8000)
- `SCAN_PORT_MAX`: Maximum port to scan (default: 9000)
- `SCAN_CONCURRENCY`: Number of concurrent scans (default: 100)

### Volumes

The Docker container mounts the following volumes:

- `./.cache:/app/.cache`: Cache directory for storing scan results

### Ports

The Docker container exposes the following ports:

- `5000`: Web application port

## Custom Configuration

To customize the configuration, you can modify the `docker-compose.yml` file or use environment variables when running the container:

```bash
docker run -d --name vllm-detector \
  -p 5000:5000 \
  -v $(pwd)/.cache:/app/.cache \
  -e SECRET_KEY=your-secret-key \
  vllm-detector:latest
```

## Building for Production

For production deployment, you may want to build a more optimized image:

```bash
docker build -t vllm-detector:prod --build-arg ENVIRONMENT=production .
```

## Troubleshooting

### Container Fails to Start

If the container fails to start, check the logs:

```bash
docker logs vllm-detector
```

### Cannot Access Web Application

If you cannot access the web application, check that:

1. The container is running: `docker ps`
2. The port is correctly mapped: `docker port vllm-detector`
3. No firewall is blocking access to port 5000
