FROM registry.gitlab.dipeak.com/dipeak/generic-repository/deploy-askbot-base:latest AS base

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    nmap \
    python3 \
    python3-pip \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY requirements.txt .

# Install Python dependencies with USTC PyPI mirror
RUN pip install --no-cache-dir -i https://mirrors.ustc.edu.cn/pypi/web/simple --trusted-host mirrors.ustc.edu.cn -r requirements.txt

# Copy application code
COPY . .

# Install the vllm-detector package in development mode
RUN pip install -e .

# Create cache directory
RUN mkdir -p .cache && chmod 777 .cache

# Copy entrypoint script
COPY docker-entrypoint.sh /app/docker-entrypoint.sh
RUN chmod +x /app/docker-entrypoint.sh

# Set environment variables
ENV PYTHONUNBUFFERED=1

# Run the entrypoint script
ENTRYPOINT ["/app/docker-entrypoint.sh"]
