# vLLM Service Model Detector

A Python service for detecting available models on a vLLM service.

## Features

- Probe vLLM servers to discover available models
- Command-line interface for easy usage
- Detailed reporting of model information

## Installation

This project uses `uv` for dependency management.

```bash
# Install uv if you don't have it
curl -sSf https://install.python-uv.org | python3

# Install dependencies
uv pip install -e .
```

## Usage

```bash
# Basic usage
vllm-detector --server-url https://your-vllm-server.com

# Get help
vllm-detector --help
```

## Development

```bash
# Install development dependencies
uv pip install -e ".[dev]"

# Run tests
pytest
```
