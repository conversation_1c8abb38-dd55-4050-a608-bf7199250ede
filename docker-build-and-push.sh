#!/bin/bash

# Default values
IMAGE_NAME="vllm-detector"
IMAGE_TAG="latest"
REGISTRY=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --tag)
      IMAGE_TAG="$2"
      shift 2
      ;;
    --registry)
      REGISTRY="$2"
      shift 2
      ;;
    --name)
      IMAGE_NAME="$2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: $0 [--tag TAG] [--registry REGISTRY] [--name IMAGE_NAME]"
      exit 1
      ;;
  esac
done

# Set full image name
if [ -n "$REGISTRY" ]; then
  FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
else
  FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
fi

echo "Building and pushing AMD64 Docker image: $FULL_IMAGE_NAME"

# Build the image
echo "Step 1: Building the image..."
if ! ./docker-build-amd64.sh --tag $IMAGE_TAG --registry "$REGISTRY" --name $IMAGE_NAME; then
  echo "Error: Failed to build the image."
  exit 1
fi

# Push the image
echo "\nStep 2: Pushing the image..."
if ! ./docker-push.sh --tag $IMAGE_TAG --registry "$REGISTRY" --name $IMAGE_NAME; then
  echo "Error: Failed to push the image."
  exit 1
fi

echo "Docker image built and pushed successfully: $FULL_IMAGE_NAME"
echo ""
echo "To pull and run the image on your target server, use:"
echo "docker pull $FULL_IMAGE_NAME"
echo "docker run -d -p 5000:5000 -v /path/to/cache:/app/.cache $FULL_IMAGE_NAME"
