#!/bin/bash

# Default values
IMAGE_NAME="vllm-detector"
IMAGE_TAG="latest"
REGISTRY=""
SCANNER="false"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --tag)
      IMAGE_TAG="$2"
      shift 2
      ;;
    --registry)
      REGISTRY="$2"
      shift 2
      ;;
    --name)
      IMAGE_NAME="$2"
      shift 2
      ;;
    --scanner)
      SCANNER="true"
      shift
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: $0 [--tag TAG] [--registry REGISTRY] [--name IMAGE_NAME] [--scanner]"
      exit 1
      ;;
  esac
done

# Export variables for docker-compose
export IMAGE_NAME=$IMAGE_NAME
export IMAGE_TAG=$IMAGE_TAG
export REGISTRY=$REGISTRY

# Create cache directory if it doesn't exist
mkdir -p cache

# Choose the appropriate docker-compose file
if [ "$SCANNER" = "true" ]; then
  COMPOSE_FILE="docker-compose.remote.scanner.yml"
  echo "Starting vLLM Detector with scanner service..."
else
  COMPOSE_FILE="docker-compose.remote.yml"
  echo "Starting vLLM Detector without scanner service..."
fi

# Pull the latest image
echo "Pulling image ${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}..."
docker pull ${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}

# Start the container
echo "Starting container using $COMPOSE_FILE..."
docker-compose -f $COMPOSE_FILE up -d

echo "Container started! Access the web interface at http://localhost:5000"
if [ "$SCANNER" = "true" ]; then
  echo "Scanner service is running in the background"
fi
echo "To stop the container: docker-compose -f $COMPOSE_FILE down"
