#!/bin/bash

# Default values
IMAGE_NAME="vllm-detector"
IMAGE_TAG="latest"
REGISTRY=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --tag)
      IMAGE_TAG="$2"
      shift 2
      ;;
    --registry)
      REGISTRY="$2"
      shift 2
      ;;
    --name)
      IMAGE_NAME="$2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: $0 [--tag TAG] [--registry REGISTRY] [--name IMAGE_NAME]"
      exit 1
      ;;
  esac
done

# Set full image name
if [ -n "$REGISTRY" ]; then
  FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
else
  FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
fi

echo "Building AMD64 Docker image: $FULL_IMAGE_NAME"

# Check Docker version
DOCKER_VERSION=$(docker version --format '{{.Server.Version}}' 2>/dev/null || docker version | grep -i "version" | head -n 1 | awk '{print $2}')
echo "Docker version: $DOCKER_VERSION"

# Enable Docker BuildKit for better build performance
export DOCKER_BUILDKIT=1

# Check if buildx is available
if ! docker buildx version > /dev/null 2>&1; then
  echo "Error: Docker buildx is not available. Please install Docker buildx."
  exit 1
fi

# Create a new builder instance if it doesn't exist
BUILDER_NAME="amd64-builder"
if ! docker buildx inspect $BUILDER_NAME > /dev/null 2>&1; then
  echo "Creating new buildx builder: $BUILDER_NAME"
  docker buildx create --name $BUILDER_NAME --use
else
  echo "Using existing buildx builder: $BUILDER_NAME"
  docker buildx use $BUILDER_NAME
fi

# Build the Docker image for AMD64 architecture
echo "Building for platform: linux/amd64"
echo "Using Dockerfile: Dockerfile.amd64"
echo "Target image: $FULL_IMAGE_NAME"

# Print Dockerfile content for debugging
echo "\nDockerfile.amd64 content:"
cat Dockerfile.amd64
echo "\n"

# Build the image with detailed output
set -x
docker buildx build --platform=linux/amd64 -t $FULL_IMAGE_NAME -f Dockerfile.amd64 --load . || {
  echo "\nERROR: Build failed!"
  echo "Check the error message above for details."
  exit 1
}
set +x

# Verify the image architecture
echo "Verifying image architecture..."
ARCH=$(docker inspect $FULL_IMAGE_NAME | grep Architecture | head -n 1 | awk -F '"' '{print $4}')
echo "Image architecture: $ARCH"

if [ "$ARCH" != "amd64" ]; then
  echo "Warning: The built image architecture is $ARCH, not amd64 as expected."
fi

echo "Docker image built successfully: $FULL_IMAGE_NAME"
echo ""
echo "To push the image to a registry, run:"
echo "docker push $FULL_IMAGE_NAME"
