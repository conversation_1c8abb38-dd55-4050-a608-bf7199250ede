#!/bin/bash
set -e

# Create cache directory if it doesn't exist
mkdir -p .cache

# Default values
HOST=${SCAN_HOST:-"localhost"}
PORT_MIN=${SCAN_PORT_MIN:-8000}
PORT_MAX=${SCAN_PORT_MAX:-9000}
CONCURRENCY=${SCAN_CONCURRENCY:-100}
WEB_PORT=${WEB_PORT:-5000}
SCAN_ENABLED=${SCAN_ENABLED:-"false"}

# Start the scanner service if enabled
if [ "$SCAN_ENABLED" = "true" ]; then
    echo "Starting scanner service for host $HOST (ports $PORT_MIN-$PORT_MAX)..."
    # Start the scanner service in the background
    python3 examples/service.py $HOST --port-min $PORT_MIN --port-max $PORT_MAX --concurrency $CONCURRENCY --cache-dir .cache &

    # Store the PID of the scanner service
    SCANNER_PID=$!
    echo "Scanner service started with PID $SCANNER_PID"
else
    echo "Scanner service is disabled. Set SCAN_ENABLED=true to enable it."
fi

# Start the web application
echo "Starting web application on port $WEB_PORT..."
python3 examples/web_app.py --host 0.0.0.0 --port $WEB_PORT --cache-dir .cache &
WEB_PID=$!
echo "Web application started with PID $WEB_PID"

# Handle shutdown signals
trap 'kill $SCANNER_PID $WEB_PID 2>/dev/null' SIGTERM SIGINT

# Keep the container running
wait $WEB_PID
