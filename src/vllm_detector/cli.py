"""Command-line interface for the vLLM detector."""

import asyncio
import logging
import sys
from typing import List, Optional, Tuple
from urllib.parse import urlparse

import typer
from rich.console import Console
from rich.logging import <PERSON><PERSON>andler
from rich.table import Table

from vllm_detector.detector import (
    ModelInfo,
    ServerInfo,
    detect_models,
    detect_servers_with_models,
)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(rich_tracebacks=True)],
)
logger = logging.getLogger("vllm_detector")

app = typer.Typer(
    help="Detect vLLM models on a server",
    add_completion=False,
)
console = Console()


@app.command(name="scan")
def scan_command(
    host: str = typer.Argument(
        ..., help="Hostname or IP address to scan for vLLM services"
    ),
    port_min: int = typer.Option(
        1, "--port-min", help="Minimum port to scan"
    ),
    port_max: int = typer.Option(
        65535, "--port-max", help="Maximum port to scan"
    ),
    timeout: int = typer.Option(
        5, help="Request timeout in seconds"
    ),
    verify_ssl: bool = typer.Option(
        True, help="Whether to verify SSL certificates"
    ),
    scan_all: bool = typer.Option(
        False, "--scan-all", help="Scan all ports even after finding a server"
    ),
    scan_all_ports: bool = typer.Option(
        True, "--scan-all-ports", help="Scan all ports even if common ports are open"
    ),
    concurrency: int = typer.Option(
        500, "--concurrency", "-c", help="Number of ports to scan concurrently"
    ),
    verbose: bool = typer.Option(
        False, "--verbose", "-v", help="Enable verbose logging"
    ),
    output_format: str = typer.Option(
        "table", "--format", "-f", help="Output format: table or json"
    ),
):
    """Scan for vLLM services on the specified host by checking all ports."""
    # Set log level based on verbosity
    if verbose:
        logger.setLevel(logging.DEBUG)

    try:
        # Run the async detection function
        servers = asyncio.run(
            detect_servers_with_models(
                host=host,
                port_range=(port_min, port_max),
                timeout=timeout,
                verify_ssl=verify_ssl,
                scan_all=scan_all,
                scan_all_ports=scan_all_ports,
                concurrency=concurrency,
            )
        )

        if not servers:
            console.print("[bold red]No vLLM services detected on the host.[/bold red]")
            sys.exit(1)

        # Display the results
        if output_format.lower() == "json":
            import json
            console.print_json(json.dumps([server.model_dump() for server in servers]))
        else:
            display_servers_table(servers)

    except Exception as e:
        logger.error(f"Error scanning for vLLM services: {e}")
        if verbose:
            import traceback
            console.print(traceback.format_exc())
        sys.exit(1)


@app.command(name="detect")
def detect_command(
    server_url: str = typer.Argument(
        ..., help="URL of the vLLM server to probe"
    ),
    timeout: int = typer.Option(
        10, help="Request timeout in seconds"
    ),
    verify_ssl: bool = typer.Option(
        True, help="Whether to verify SSL certificates"
    ),
    verbose: bool = typer.Option(
        False, "--verbose", "-v", help="Enable verbose logging"
    ),
    output_format: str = typer.Option(
        "table", "--format", "-f", help="Output format: table or json"
    ),
):
    """Detect vLLM models on the specified server URL."""
    # Set log level based on verbosity
    if verbose:
        logger.setLevel(logging.DEBUG)

    try:
        # Run the async detection function
        models = asyncio.run(
            detect_models(
                server_url=server_url,
                timeout=timeout,
                verify_ssl=verify_ssl,
            )
        )

        if not models:
            console.print("[bold red]No models detected on the server.[/bold red]")
            sys.exit(1)

        # Display the results
        if output_format.lower() == "json":
            import json
            console.print_json(json.dumps([model.model_dump() for model in models]))
        else:
            display_models_table(models)

    except Exception as e:
        logger.error(f"Error detecting models: {e}")
        if verbose:
            import traceback
            console.print(traceback.format_exc())
        sys.exit(1)


def display_servers_table(servers: List[ServerInfo]):
    """Display the detected servers and their models in a table.

    Args:
        servers: List of detected servers
    """
    table = Table(title="Detected vLLM Services")

    table.add_column("Server URL", style="cyan")
    table.add_column("Port", style="green")
    table.add_column("Models", style="yellow")

    for server in servers:
        model_names = ", ".join([model.model_id for model in server.models])
        table.add_row(
            server.url,
            str(server.port),
            model_names if model_names else "Unknown",
        )

    console.print(table)

    # If there are models, also display the models table
    all_models = []
    for server in servers:
        for model in server.models:
            all_models.append(model)

    if all_models:
        console.print("\n")
        display_models_table(all_models)


def display_models_table(models: List[ModelInfo]):
    """Display the detected models in a table.

    Args:
        models: List of detected models
    """
    table = Table(title="Detected vLLM Models")

    table.add_column("Model ID", style="cyan")
    table.add_column("Server URL", style="blue")
    table.add_column("Max Length", style="green")
    table.add_column("Data Type", style="yellow")
    table.add_column("Device", style="magenta")

    for model in models:
        table.add_row(
            model.model_id,
            model.server_url or "Unknown",
            str(model.max_model_len) if model.max_model_len else "Unknown",
            model.dtype or "Unknown",
            model.device_type or "Unknown",
        )

    console.print(table)


if __name__ == "__main__":
    app()
