"""Core functionality for detecting vLLM models on a server."""

import asyncio
import logging
import socket
from typing import Dict, List, Optional, Tuple, Union
from urllib.parse import urlparse

import httpx
from pydantic import BaseModel, Field

logger = logging.getLogger("vllm_detector")


class ServerInfo(BaseModel):
    """Information about a server with vLLM service."""

    host: str
    port: int
    url: str
    is_available: bool = False
    models: List["ModelInfo"] = []


class ModelInfo(BaseModel):
    """Information about a vLLM model."""

    model_id: str
    max_model_len: Optional[int] = None
    dtype: Optional[str] = None
    device_type: Optional[str] = None
    is_available: bool = True
    error: Optional[str] = None
    server_url: Optional[str] = None


class VLLMDetector:
    """Detector for vLLM service models."""

    def __init__(
        self,
        server_url: str,
        timeout: int = 10,
        verify_ssl: bool = True,
    ):
        """Initialize the detector.

        Args:
            server_url: URL of the vLLM server
            timeout: Request timeout in seconds
            verify_ssl: Whether to verify SSL certificates
        """
        self.server_url = server_url.rstrip("/")
        self.timeout = timeout
        self.verify_ssl = verify_ssl
        self.client = httpx.AsyncClient(
            timeout=timeout,
            verify=verify_ssl,
        )

    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()

    async def detect_models(self) -> List[ModelInfo]:
        """Detect available models on the vLLM server.

        Returns:
            List of detected models with their information
        """
        models = []
        logger.info(f"Detecting models on {self.server_url}")

        # Try different endpoints that vLLM services might expose
        endpoints = [
            "/v1/models",  # OpenAI-compatible API
        ]

        logger.info(f"Trying {len(endpoints)} standard API endpoints...")
        for endpoint in endpoints:
            try:
                url = f"{self.server_url}{endpoint}"
                logger.info(f"Trying endpoint: {url}")

                logger.info(f"Sending GET request to {url}")
                response = await self.client.get(url)
                response.raise_for_status()
                logger.info(f"Received response from {url} (status: {response.status_code})")

                data = response.json()
                logger.info(f"Parsed JSON response from {url}")

                # Parse the response based on the endpoint format
                if endpoint == "/v1/models":
                    logger.info(f"Processing OpenAI-compatible format from {endpoint}")
                    # OpenAI-compatible format
                    if "data" in data:
                        logger.info(f"Found {len(data['data'])} models in OpenAI format")
                        for model_data in data["data"]:
                            model_id = model_data.get("id")
                            logger.info(f"Found model: {model_id}")
                            models.append(
                                ModelInfo(
                                    model_id=model_id,
                                    max_model_len=model_data.get("context_length"),
                                    dtype=model_data.get("dtype"),
                                    device_type=model_data.get("device_type"),
                                )
                            )
                elif endpoint == "/models":
                    logger.info(f"Processing standard models format from {endpoint}")
                    # Simple list of models or dict with model info
                    if isinstance(data, list):
                        logger.info(f"Found list of {len(data)} models")
                        for model_id in data:
                            if isinstance(model_id, str):
                                logger.info(f"Found model: {model_id}")
                                models.append(ModelInfo(model_id=model_id))
                            elif isinstance(model_id, dict) and "id" in model_id:
                                logger.info(f"Found model: {model_id['id']}")
                                models.append(
                                    ModelInfo(
                                        model_id=model_id["id"],
                                        max_model_len=model_id.get("max_model_len"),
                                        dtype=model_id.get("dtype"),
                                        device_type=model_id.get("device_type"),
                                    )
                                )
                    elif isinstance(data, dict) and "models" in data:
                        logger.info(f"Found dictionary with {len(data['models'])} models")
                        for model_id, model_info in data["models"].items():
                            logger.info(f"Found model: {model_id}")
                            if isinstance(model_info, dict):
                                models.append(
                                    ModelInfo(
                                        model_id=model_id,
                                        max_model_len=model_info.get("max_model_len"),
                                        dtype=model_info.get("dtype"),
                                        device_type=model_info.get("device_type"),
                                    )
                                )
                            else:
                                models.append(ModelInfo(model_id=model_id))
                elif endpoint == "/info":
                    logger.info(f"Processing info format from {endpoint}")
                    # Info endpoint might contain model info
                    if "model" in data:
                        model_id = data["model"]
                        logger.info(f"Found model in info: {model_id}")
                        models.append(
                            ModelInfo(
                                model_id=model_id,
                                max_model_len=data.get("max_model_len"),
                                dtype=data.get("dtype"),
                                device_type=data.get("device_type"),
                            )
                        )
                    elif "models" in data:
                        logger.info(f"Found models list in info with {len(data['models'])} models")
                        for model_id in data["models"]:
                            if isinstance(model_id, str):
                                logger.info(f"Found model: {model_id}")
                                models.append(ModelInfo(model_id=model_id))
                            elif isinstance(model_id, dict) and "id" in model_id:
                                logger.info(f"Found model: {model_id['id']}")
                                models.append(
                                    ModelInfo(
                                        model_id=model_id["id"],
                                        max_model_len=model_id.get("max_model_len"),
                                        dtype=model_id.get("dtype"),
                                        device_type=model_id.get("device_type"),
                                    )
                                )

                # If we found models, no need to try other endpoints
                if models:
                    logger.info(f"Found {len(models)} models via {endpoint} endpoint")
                    break
                else:
                    logger.info(f"No models found via {endpoint} endpoint")

            except httpx.HTTPStatusError as e:
                logger.info(f"HTTP error for {endpoint}: {e}")
                continue
            except httpx.RequestError as e:
                logger.info(f"Request error for {endpoint}: {e}")
                continue
            except Exception as e:
                logger.info(f"Unexpected error for {endpoint}: {e}")
                continue

        # If no models found through standard endpoints, try to probe with a completion request
        if not models:
            logger.info("No models found via standard endpoints, trying completion probe...")
            try:
                # Try to get model info from a simple completion request
                model = await self._probe_with_completion()
                if model:
                    logger.info(f"Found model via completion probe: {model.model_id}")
                    models.append(model)
                else:
                    logger.info("No models found via completion probe")
            except Exception as e:
                logger.info(f"Error probing with completion: {e}")

        if models:
            logger.info(f"Total models detected: {len(models)}")
            for model in models:
                logger.info(f"  - {model.model_id} (max_len: {model.max_model_len}, dtype: {model.dtype}, device: {model.device_type})")
        else:
            logger.info("No models detected on this server")

        return models

    async def _probe_with_completion(self) -> Optional[ModelInfo]:
        """Probe the server with a simple completion request to detect the model.

        Returns:
            ModelInfo if a model is detected, None otherwise
        """
        # Try OpenAI-compatible endpoint
        logger.info(f"Probing with OpenAI-compatible completion endpoint...")
        try:
            url = f"{self.server_url}/v1/completions"
            logger.info(f"Sending POST request to {url}")
            response = await self.client.post(
                url,
                json={
                    "prompt": "Hello",
                    "max_tokens": 1,
                    "temperature": 0,
                },
            )
            response.raise_for_status()
            logger.info(f"Received response from {url} (status: {response.status_code})")

            data = response.json()
            logger.info(f"Parsed JSON response from {url}")

            if "model" in data:
                model_id = data["model"]
                logger.info(f"Found model via OpenAI completion: {model_id}")
                return ModelInfo(model_id=model_id)
            else:
                logger.info(f"No model information found in OpenAI completion response")

        except Exception as e:
            logger.info(f"Error probing with OpenAI completion: {e}")

        # Try vLLM-specific endpoint
        logger.info(f"Probing with vLLM-specific generate endpoint...")
        try:
            url = f"{self.server_url}/generate"
            logger.info(f"Sending POST request to {url}")
            response = await self.client.post(
                url,
                json={
                    "prompt": "Hello",
                    "max_tokens": 1,
                    "temperature": 0,
                },
            )
            response.raise_for_status()
            logger.info(f"Received response from {url} (status: {response.status_code})")

            data = response.json()
            logger.info(f"Parsed JSON response from {url}")

            if "model" in data:
                model_id = data["model"]
                logger.info(f"Found model via vLLM generate: {model_id}")
                return ModelInfo(model_id=model_id)
            else:
                logger.info(f"No model information found in vLLM generate response")

        except Exception as e:
            logger.info(f"Error probing with vLLM generate: {e}")

        logger.info("Completion probing failed to detect any models")
        return None


async def scan_single_port(host: str, port: int, timeout: float = 0.5) -> Tuple[int, bool]:
    """Scan a single port to check if it's open.

    Args:
        host: Hostname or IP address to scan
        port: Port number to scan
        timeout: Socket timeout in seconds

    Returns:
        Tuple of (port, is_open)
    """
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        is_open = (result == 0)
        sock.close()
        return port, is_open
    except Exception as e:
        logger.debug(f"Error scanning port {port}: {e}")
        return port, False


async def scan_ports_concurrent(host: str, ports: List[int], timeout: float = 0.5, batch_size: int = 100) -> List[int]:
    """Scan a list of ports concurrently.

    Args:
        host: Hostname or IP address to scan
        ports: List of ports to scan
        timeout: Socket timeout in seconds
        batch_size: Number of ports to scan concurrently

    Returns:
        List of open ports
    """
    open_ports = []
    total_ports = len(ports)
    ports_checked = 0
    ports_open = 0
    last_progress = 0

    # Process ports in batches to avoid creating too many tasks at once
    for i in range(0, total_ports, batch_size):
        batch = ports[i:i + batch_size]
        tasks = [scan_single_port(host, port, timeout) for port in batch]
        results = await asyncio.gather(*tasks)

        for port, is_open in results:
            ports_checked += 1
            if is_open:
                open_ports.append(port)
                ports_open += 1
                logger.info(f"Found open port: {port}")

        # Print progress every 5% or 1000 ports, whichever is smaller
        progress = int(ports_checked / total_ports * 100)
        if (progress >= last_progress + 5 or ports_checked % 1000 == 0) and ports_checked > 0:
            logger.info(f"Scan progress: {progress}% ({ports_checked}/{total_ports} ports), found {ports_open} open ports")
            last_progress = progress

        # Yield control to avoid blocking UI
        await asyncio.sleep(0)

    return open_ports


async def scan_ports(host: str, port_range: Tuple[int, int] = (1, 65535), common_ports: List[int] = None, scan_all_ports: bool = True, concurrency: int = 500) -> List[int]:
    """Scan for open ports on the specified host.

    Args:
        host: Hostname or IP address to scan
        port_range: Range of ports to scan (min, max)
        common_ports: List of common ports to prioritize scanning
        scan_all_ports: Whether to scan all ports even if common ports are open
        concurrency: Number of ports to scan concurrently

    Returns:
        List of open ports
    """
    open_ports = []

    # Default common ports for vLLM services
    if common_ports is None:
        common_ports = [
            80, 443,  # HTTP/HTTPS
            8000, 8080, 8888, 8889, 8890,  # Common web server ports
            5000, 5001, 5002, 5003,  # Flask default ports
            3000, 3001, 3002,  # Node.js/React default ports
            7860, 7861, 7862,  # Gradio default ports
            8501, 8502, 8503,  # Streamlit default ports
        ]

    logger.info(f"Starting port scan on {host} with concurrency {concurrency}")
    logger.info(f"First scanning {len(common_ports)} common ports: {common_ports}")

    # First check common ports concurrently
    common_ports_to_scan = [p for p in common_ports if port_range[0] <= p <= port_range[1]]
    if common_ports_to_scan:
        common_open_ports = await scan_ports_concurrent(host, common_ports_to_scan, timeout=0.5, batch_size=concurrency)
        open_ports.extend(common_open_ports)
        logger.info(f"Scanned {len(common_ports_to_scan)} common ports, found {len(common_open_ports)} open")

    # Then scan the rest of the range if needed
    if port_range[0] < port_range[1] and (scan_all_ports or len(open_ports) == 0):
        # Use a more limited range if requested
        limited_range = (port_range[0], min(port_range[1], 65535))  # Allow scanning up to 65535 ports
        if len(open_ports) == 0:
            logger.info(f"No common ports found open. Scanning range {limited_range[0]}-{limited_range[1]}...")
        else:
            logger.info(f"Found {len(open_ports)} common ports open. Continuing to scan range {limited_range[0]}-{limited_range[1]}...")

        # Create a list of ports to scan, excluding already scanned common ports
        ports_to_scan = [p for p in range(limited_range[0], limited_range[1] + 1)
                        if p not in common_ports_to_scan]

        # Scan the remaining ports concurrently
        remaining_open_ports = await scan_ports_concurrent(host, ports_to_scan, timeout=0.2, batch_size=concurrency)
        open_ports.extend(remaining_open_ports)

        logger.info(f"Scan completed: checked {len(ports_to_scan)} ports, found {len(remaining_open_ports)} open")

    return sorted(open_ports)


async def detect_servers_with_models(
    host: str,
    port_range: Tuple[int, int] = (1, 65535),
    common_ports: List[int] = None,
    timeout: int = 5,
    verify_ssl: bool = True,
    scan_all: bool = False,
    scan_all_ports: bool = True,
    concurrency: int = 500,
    open_ports: Optional[List[int]] = None,
) -> List[ServerInfo]:
    """Detect vLLM servers and their models on the specified host.

    Args:
        host: Hostname or IP address to scan
        port_range: Range of ports to scan (min, max)
        common_ports: List of common ports to prioritize scanning
        timeout: Request timeout in seconds
        verify_ssl: Whether to verify SSL certificates
        scan_all: Whether to scan all ports in range or stop after finding one server
        scan_all_ports: Whether to scan all ports even if common ports are open
        concurrency: Number of ports to scan concurrently
        open_ports: Optional list of open ports to check instead of scanning

    Returns:
        List of servers with their models
    """
    servers = []
    logger.info(f"Starting vLLM service detection on {host}")
    logger.info(f"Port range: {port_range[0]}-{port_range[1]}")
    logger.info(f"Timeout: {timeout} seconds, Verify SSL: {verify_ssl}, Scan all: {scan_all}")

    # Use provided open ports or scan for them
    if open_ports is not None:
        logger.info(f"Using {len(open_ports)} provided open ports instead of scanning")
    else:
        logger.info("Step 1: Scanning for open ports...")
        open_ports = await scan_ports(host, port_range, common_ports, scan_all_ports, concurrency)
        logger.info(f"Found {len(open_ports)} open ports on {host}: {open_ports}")

    if not open_ports:
        logger.info("No open ports found. Exiting.")
        return servers

    # Check each open port for vLLM service
    logger.info("Step 2: Checking open ports for vLLM services...")
    ports_checked = 0
    services_found = 0

    for port in open_ports:
        ports_checked += 1
        logger.info(f"Checking port {port} ({ports_checked}/{len(open_ports)})")

        # Try both HTTP and HTTPS
        for protocol in ["http", "https"]:
            server_url = f"{protocol}://{host}:{port}"
            logger.info(f"Trying {server_url}")

            try:
                # Create a detector for this server
                logger.info(f"Connecting to {server_url} (timeout: {timeout}s)")
                detector = VLLMDetector(
                    server_url=server_url,
                    timeout=timeout,
                    verify_ssl=verify_ssl,
                )

                # Try to detect models
                logger.info(f"Detecting models on {server_url}...")
                models = await detector.detect_models()
                await detector.close()

                if models:
                    services_found += 1
                    # Update model info with server URL
                    for model in models:
                        model.server_url = server_url

                    # Create server info
                    server_info = ServerInfo(
                        host=host,
                        port=port,
                        url=server_url,
                        is_available=True,
                        models=models,
                    )

                    servers.append(server_info)
                    logger.info(f"SUCCESS: Found vLLM service at {server_url} with {len(models)} models")
                    logger.info(f"Models found: {', '.join([model.model_id for model in models])}")

                    # Stop after finding one server if scan_all is False
                    if not scan_all and servers:
                        logger.info(f"Found a server and scan_all=False. Stopping scan.")
                        return servers
                else:
                    logger.info(f"No models found on {server_url}")
            except Exception as e:
                logger.debug(f"Error checking {server_url}: {e}")

    logger.info(f"Scan completed: checked {ports_checked} ports, found {services_found} vLLM services")

    return servers


async def detect_models(
    server_url: str,
    timeout: int = 10,
    verify_ssl: bool = True,
) -> List[ModelInfo]:
    """Detect vLLM models on the specified server.

    Args:
        server_url: URL of the vLLM server
        timeout: Request timeout in seconds
        verify_ssl: Whether to verify SSL certificates

    Returns:
        List of detected models with their information
    """
    detector = VLLMDetector(
        server_url=server_url,
        timeout=timeout,
        verify_ssl=verify_ssl,
    )

    try:
        models = await detector.detect_models()
        # Update model info with server URL
        for model in models:
            model.server_url = server_url
        return models
    finally:
        await detector.close()
