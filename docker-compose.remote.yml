version: '3'

services:
  vllm-detector:
    image: ${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}
    ports:
      - "5000:5000"
    volumes:
      - ./cache:/app/.cache
    environment:
      - PYTHONUNBUFFERED=1
      - SECRET_KEY=vllm-detector-secret-key
      - WEB_PORT=5000
      - SCAN_ENABLED=false
      - SCAN_HOST=localhost
      - SCAN_PORT_MIN=8000
      - SCAN_PORT_MAX=9000
      - SCAN_CONCURRENCY=100
    restart: unless-stopped
