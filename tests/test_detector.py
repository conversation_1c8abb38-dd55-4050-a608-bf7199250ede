"""Tests for the vLLM detector."""

import asyncio
import json
from unittest.mock import AsyncMock, MagicMock, patch

import httpx
import pytest

from vllm_detector.detector import <PERSON><PERSON>n<PERSON>, VLLMDetector, detect_models


@pytest.fixture
def mock_response():
    """Create a mock HTTP response."""
    response = MagicMock()
    response.raise_for_status = MagicMock()
    return response


@pytest.fixture
def mock_client():
    """Create a mock HTTP client."""
    client = AsyncMock()
    client.aclose = AsyncMock()
    return client


@pytest.mark.asyncio
async def test_detect_models_openai_format(mock_response, mock_client):
    """Test detecting models with OpenAI-compatible API format."""
    # Mock response data
    mock_response.json.return_value = {
        "data": [
            {
                "id": "gpt-3.5-turbo",
                "context_length": 4096,
                "dtype": "float16",
                "device_type": "cuda",
            },
            {
                "id": "llama-7b",
                "context_length": 2048,
                "dtype": "float16",
                "device_type": "cuda",
            },
        ]
    }
    
    # Set up the mock client to return our mock response
    mock_client.get.return_value = mock_response
    
    # Create detector with mock client
    detector = VLLMDetector("http://localhost:8000")
    detector.client = mock_client
    
    # Test detection
    models = await detector.detect_models()
    
    # Verify results
    assert len(models) == 2
    assert models[0].model_id == "gpt-3.5-turbo"
    assert models[0].max_model_len == 4096
    assert models[0].dtype == "float16"
    assert models[0].device_type == "cuda"
    assert models[1].model_id == "llama-7b"
    assert models[1].max_model_len == 2048


@pytest.mark.asyncio
async def test_detect_models_simple_list(mock_response, mock_client):
    """Test detecting models with a simple list format."""
    # Mock response data
    mock_response.json.return_value = ["gpt-3.5-turbo", "llama-7b"]
    
    # Set up the mock client to return our mock response
    mock_client.get.return_value = mock_response
    
    # Create detector with mock client
    detector = VLLMDetector("http://localhost:8000")
    detector.client = mock_client
    
    # Test detection
    models = await detector.detect_models()
    
    # Verify results
    assert len(models) == 2
    assert models[0].model_id == "gpt-3.5-turbo"
    assert models[1].model_id == "llama-7b"


@pytest.mark.asyncio
async def test_detect_models_info_endpoint(mock_response, mock_client):
    """Test detecting models with the info endpoint."""
    # Mock response data
    mock_response.json.return_value = {
        "model": "llama-7b",
        "max_model_len": 2048,
        "dtype": "float16",
        "device_type": "cuda",
    }
    
    # Set up the mock client to return our mock response
    mock_client.get.return_value = mock_response
    
    # Create detector with mock client
    detector = VLLMDetector("http://localhost:8000")
    detector.client = mock_client
    
    # Test detection
    models = await detector.detect_models()
    
    # Verify results
    assert len(models) == 1
    assert models[0].model_id == "llama-7b"
    assert models[0].max_model_len == 2048
    assert models[0].dtype == "float16"
    assert models[0].device_type == "cuda"


@pytest.mark.asyncio
async def test_probe_with_completion(mock_response, mock_client):
    """Test probing with a completion request."""
    # Mock response data
    mock_response.json.return_value = {
        "model": "llama-7b",
        "choices": [{"text": "Hello world"}],
    }
    
    # Set up the mock client to return our mock response
    mock_client.post.return_value = mock_response
    
    # Create detector with mock client
    detector = VLLMDetector("http://localhost:8000")
    detector.client = mock_client
    
    # Test probing
    model = await detector._probe_with_completion()
    
    # Verify results
    assert model is not None
    assert model.model_id == "llama-7b"


@pytest.mark.asyncio
async def test_detect_models_function():
    """Test the detect_models function."""
    # Mock the VLLMDetector class
    with patch("vllm_detector.detector.VLLMDetector") as mock_detector_class:
        # Set up the mock detector instance
        mock_detector = AsyncMock()
        mock_detector.detect_models.return_value = [
            ModelInfo(model_id="gpt-3.5-turbo"),
            ModelInfo(model_id="llama-7b"),
        ]
        mock_detector_class.return_value = mock_detector
        
        # Test the function
        models = await detect_models("http://localhost:8000")
        
        # Verify results
        assert len(models) == 2
        assert models[0].model_id == "gpt-3.5-turbo"
        assert models[1].model_id == "llama-7b"
        
        # Verify the detector was closed
        mock_detector.close.assert_called_once()
