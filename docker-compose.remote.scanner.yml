version: '3'

services:
  vllm-detector-scanner:
    image: ${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}
    ports:
      - "5000:5000"
    volumes:
      - ./cache:/app/.cache
    environment:
      - PYTHONUNBUFFERED=1
      - SECRET_KEY=vllm-detector-secret-key
      - WEB_PORT=5000
      - SCAN_ENABLED=true
      - SCAN_HOST=localhost  # Change this to the host you want to scan
      - SCAN_PORT_MIN=8000
      - SCAN_PORT_MAX=9000
      - SCAN_CONCURRENCY=100
    restart: unless-stopped
