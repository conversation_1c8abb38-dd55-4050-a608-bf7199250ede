 * Serving Flask app 'web_app'
 * Debug mode: off
[15:41:21] INFO     [31m[1mWARNING: This is a development        _internal.py:97
                    server. Do not use it in a production                       
                    deployment. Use a production WSGI server                    
                    instead.[0m                                                 
                     * Running on all addresses (0.0.0.0)                       
                     * Running on http://127.0.0.1:5001                         
                     * Running on http://**************:5001                    
           INFO     [33mPress CTRL+C to quit[0m                  _internal.py:97
[15:41:25] INFO     127.0.0.1 - - [22/Apr/2025 15:41:25] "POST   _internal.py:97
                    /test-service HTTP/1.1" 200 -                               
           INFO     Testing vLLM service at                       web_app.py:153
                    http://**************:8087 with prompt:                     
                    Hello, model_id: baowu-glm-4-9b                             
           INFO     Using model: baowu-glm-4-9b                   web_app.py:161
           INFO     127.0.0.1 - - [22/Apr/2025 15:41:25] "POST   _internal.py:97
                    /api/get-models HTTP/1.1" 200 -                             
           INFO     127.0.0.1 - - [22/Apr/2025 15:41:25] "GET    _internal.py:97
                    /api/get-all-services HTTP/1.1" 200 -                       
           INFO     Testing endpoint:                             web_app.py:236
                    http://**************:8087/v1/chat/completion               
                    s with payload type: ['messages',                           
                    'max_tokens', 'temperature']                                
           INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:8087/v1/chat/completio                
                    ns "HTTP/1.1 200 OK"                                        
           INFO     Response status: 200                          web_app.py:245
           INFO     Response: {'id':                              web_app.py:248
                    'chat-7d046d289f8e4149b397d46e2243207a',                    
                    'object': 'chat.completion', 'created':                     
                    1745307685, 'model': 'baowu-glm-4-9b',                      
                    'choices': [{'index': 0, 'message': {'role':                
                    'assistant', 'content': '{"', 'tool_calls':                 
                    []}, 'logprobs': None, 'finish_reason':                     
                    'length', 'stop_reason': None}], 'usage':                   
                    {'prompt_tokens': 6, 'total_tokens': 7,                     
                    'completion_tokens': 1}, 'prompt_logprobs':                 
                    None}                                                       
           INFO     Successfully tested vLLM service at           web_app.py:254
                    http://**************:8087/v1/chat/completion               
                    s                                                           
           INFO     127.0.0.1 - - [22/Apr/2025 15:41:25] "POST   _internal.py:97
                    /api/ping-service HTTP/1.1" 200 -                           
[15:41:40] INFO     127.0.0.1 - - [22/Apr/2025 15:41:40] "GET /  _internal.py:97
                    HTTP/1.1" 200 -                                             
[15:41:41] INFO     127.0.0.1 - - [22/Apr/2025 15:41:41] "GET    _internal.py:97
                    /host/************** HTTP/1.1" 200 -                        
[15:41:49] INFO     127.0.0.1 - - [22/Apr/2025 15:41:49] "POST   _internal.py:97
                    /test-service HTTP/1.1" 200 -                               
           INFO     Testing vLLM service at                       web_app.py:153
                    http://**************:8087 with prompt:                     
                    Hello, model_id: baowu-glm-4-9b                             
           INFO     Using model: baowu-glm-4-9b                   web_app.py:161
           INFO     127.0.0.1 - - [22/Apr/2025 15:41:49] "POST   _internal.py:97
                    /api/get-models HTTP/1.1" 200 -                             
           INFO     127.0.0.1 - - [22/Apr/2025 15:41:49] "GET    _internal.py:97
                    /api/get-all-services HTTP/1.1" 200 -                       
           INFO     Testing endpoint:                             web_app.py:236
                    http://**************:8087/v1/chat/completion               
                    s with payload type: ['messages',                           
                    'max_tokens', 'temperature']                                
           INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:8087/v1/chat/completio                
                    ns "HTTP/1.1 200 OK"                                        
           INFO     Response status: 200                          web_app.py:245
           INFO     Response: {'id':                              web_app.py:248
                    'chat-429f52d6e0dd4adca613cc4ca55f58d9',                    
                    'object': 'chat.completion', 'created':                     
                    1745307709, 'model': 'baowu-glm-4-9b',                      
                    'choices': [{'index': 0, 'message': {'role':                
                    'assistant', 'content': '{"', 'tool_calls':                 
                    []}, 'logprobs': None, 'finish_reason':                     
                    'length', 'stop_reason': None}], 'usage':                   
                    {'prompt_tokens': 6, 'total_tokens': 7,                     
                    'completion_tokens': 1}, 'prompt_logprobs':                 
                    None}                                                       
           INFO     Successfully tested vLLM service at           web_app.py:254
                    http://**************:8087/v1/chat/completion               
                    s                                                           
           INFO     127.0.0.1 - - [22/Apr/2025 15:41:49] "POST   _internal.py:97
                    /api/ping-service HTTP/1.1" 200 -                           
[15:41:53] INFO     127.0.0.1 - - [22/Apr/2025 15:41:53] "POST   _internal.py:97
                    /api/get-models HTTP/1.1" 200 -                             
           INFO     Testing vLLM service at                       web_app.py:153
                    http://**************:8090 with prompt:                     
                    Hello, model_id: baowu-glm-4-9b                             
           INFO     Using model: baowu-glm-4-9b                   web_app.py:161
           INFO     Testing endpoint:                             web_app.py:236
                    http://**************:8090/v1/chat/completion               
                    s with payload type: ['messages',                           
                    'max_tokens', 'temperature']                                
           INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:8090/v1/chat/completio                
                    ns "HTTP/1.1 404 Not Found"                                 
           INFO     Response status: 404                          web_app.py:245
           INFO     Endpoint                                      web_app.py:257
                    http://**************:8090/v1/chat/completion               
                    s returned status 404:                                      
                    {"object":"error","message":"The model                      
                    `baowu-glm-4-9b` does not                                   
                    exist.","type":"NotFoundError","par                         
           INFO     Testing endpoint:                             web_app.py:236
                    http://**************:8090/v1/completions                   
                    with payload type: ['prompt', 'max_tokens',                 
                    'temperature']                                              
[15:41:54] INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:8090/v1/completions                   
                    "HTTP/1.1 404 Not Found"                                    
           INFO     Response status: 404                          web_app.py:245
           INFO     Endpoint                                      web_app.py:257
                    http://**************:8090/v1/completions                   
                    returned status 404:                                        
                    {"object":"error","message":"The model                      
                    `baowu-glm-4-9b` does not                                   
                    exist.","type":"NotFoundError","par                         
           INFO     Testing endpoint:                             web_app.py:236
                    http://**************:8090/generate with                    
                    payload type: ['prompt', 'max_tokens',                      
                    'temperature']                                              
           INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:8090/generate                         
                    "HTTP/1.1 404 Not Found"                                    
           INFO     Response status: 404                          web_app.py:245
           INFO     Endpoint http://**************:8090/generate  web_app.py:257
                    returned status 404: {"detail":"Not Found"}                 
           INFO     Testing endpoint:                             web_app.py:236
                    http://**************:8090/api/generate with                
                    payload type: ['prompt', 'max_tokens',                      
                    'temperature']                                              
           INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:8090/api/generate                     
                    "HTTP/1.1 404 Not Found"                                    
           INFO     Response status: 404                          web_app.py:245
           INFO     Endpoint                                      web_app.py:257
                    http://**************:8090/api/generate                     
                    returned status 404: {"detail":"Not Found"}                 
           INFO     Testing endpoint: http://**************:8090/ web_app.py:236
                    with payload type: ['prompt', 'max_tokens',                 
                    'temperature']                                              
           INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:8090/ "HTTP/1.1 404                   
                    Not Found"                                                  
           INFO     Response status: 404                          web_app.py:245
           INFO     Endpoint http://**************:8090/ returned web_app.py:257
                    status 404: {"detail":"Not Found"}                          
           ERROR    Failed to test vLLM service at                web_app.py:263
                    http://**************:8090                                  
           INFO     127.0.0.1 - - [22/Apr/2025 15:41:54] "POST   _internal.py:97
                    /api/ping-service HTTP/1.1" 200 -                           
[15:41:56] INFO     Testing vLLM service at                       web_app.py:153
                    http://**************:8090 with prompt:                     
                    Hello, model_id: pre-glm-4-9b-agent                         
           INFO     Using model: pre-glm-4-9b-agent               web_app.py:161
           INFO     Testing endpoint:                             web_app.py:236
                    http://**************:8090/v1/chat/completion               
                    s with payload type: ['messages',                           
                    'max_tokens', 'temperature']                                
           INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:8090/v1/chat/completio                
                    ns "HTTP/1.1 200 OK"                                        
           INFO     Response status: 200                          web_app.py:245
           INFO     Response: {'id':                              web_app.py:248
                    'chat-f0c0a4c0bc6d43628506f3be2c49ea71',                    
                    'object': 'chat.completion', 'created':                     
                    1745307716, 'model': 'pre-glm-4-9b-agent',                  
                    'choices': [{'index': 0, 'message': {'role':                
                    'assistant', 'content': 'Alright',                          
                    'tool_calls': []}, 'logprobs': None,                        
                    'finish_reason': 'length', 'stop_reason':                   
                    None}], 'usage': {'prompt_tokens': 6,                       
                    'total_tokens': 7, 'completion_tokens': 1},                 
                    'prompt_logprobs': None}                                    
           INFO     Successfully tested vLLM service at           web_app.py:254
                    http://**************:8090/v1/chat/completion               
                    s                                                           
           INFO     127.0.0.1 - - [22/Apr/2025 15:41:56] "POST   _internal.py:97
                    /api/ping-service HTTP/1.1" 200 -                           
[15:41:59] INFO     Streaming chat with vLLM service at           web_app.py:277
                    http://**************:8090 with 1 messages                  
           INFO     Using model: pre-glm-4-9b-agent               web_app.py:285
           INFO     Trying streaming endpoint:                    web_app.py:332
                    http://**************:8090/v1/chat/completion               
                    s                                                           
[15:42:00] INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:8090/v1/chat/completio                
                    ns "HTTP/1.1 200 OK"                                        
           INFO     127.0.0.1 - - [22/Apr/2025 15:42:00] "POST   _internal.py:97
                    /api/chat HTTP/1.1" 200 -                                   
[15:42:02] INFO     127.0.0.1 - - [22/Apr/2025 15:42:02] "POST   _internal.py:97
                    /api/get-models HTTP/1.1" 200 -                             
           INFO     Testing vLLM service at                       web_app.py:153
                    http://**************:29001 with prompt:                    
                    Hello, model_id: pre-glm-4-9b-agent                         
           INFO     Using model: pre-glm-4-9b-agent               web_app.py:161
[15:42:03] INFO     Testing endpoint:                             web_app.py:236
                    http://**************:29001/v1/chat/completio               
                    ns with payload type: ['messages',                          
                    'max_tokens', 'temperature']                                
           INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:29001/v1/chat/completi                
                    ons "HTTP/1.1 404 Not Found"                                
           INFO     Response status: 404                          web_app.py:245
           INFO     Endpoint                                      web_app.py:257
                    http://**************:29001/v1/chat/completio               
                    ns returned status 404:                                     
                    {"object":"error","message":"The model                      
                    `pre-glm-4-9b-agent` does not                               
                    exist.","type":"NotFoundError",                             
           INFO     Testing endpoint:                             web_app.py:236
                    http://**************:29001/v1/completions                  
                    with payload type: ['prompt', 'max_tokens',                 
                    'temperature']                                              
           INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:29001/v1/completions                  
                    "HTTP/1.1 404 Not Found"                                    
           INFO     Response status: 404                          web_app.py:245
           INFO     Endpoint                                      web_app.py:257
                    http://**************:29001/v1/completions                  
                    returned status 404:                                        
                    {"object":"error","message":"The model                      
                    `pre-glm-4-9b-agent` does not                               
                    exist.","type":"NotFoundError",                             
           INFO     Testing endpoint:                             web_app.py:236
                    http://**************:29001/generate with                   
                    payload type: ['prompt', 'max_tokens',                      
                    'temperature']                                              
           INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:29001/generate                        
                    "HTTP/1.1 404 Not Found"                                    
           INFO     Response status: 404                          web_app.py:245
           INFO     Endpoint http://**************:29001/generate web_app.py:257
                    returned status 404: {"detail":"Not Found"}                 
           INFO     Testing endpoint:                             web_app.py:236
                    http://**************:29001/api/generate with               
                    payload type: ['prompt', 'max_tokens',                      
                    'temperature']                                              
           INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:29001/api/generate                    
                    "HTTP/1.1 404 Not Found"                                    
           INFO     Response status: 404                          web_app.py:245
           INFO     Endpoint                                      web_app.py:257
                    http://**************:29001/api/generate                    
                    returned status 404: {"detail":"Not Found"}                 
           INFO     Testing endpoint:                             web_app.py:236
                    http://**************:29001/ with payload                   
                    type: ['prompt', 'max_tokens', 'temperature']               
           INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:29001/ "HTTP/1.1 404                  
                    Not Found"                                                  
           INFO     Response status: 404                          web_app.py:245
           INFO     Endpoint http://**************:29001/         web_app.py:257
                    returned status 404: {"detail":"Not Found"}                 
           ERROR    Failed to test vLLM service at                web_app.py:263
                    http://**************:29001                                 
           INFO     127.0.0.1 - - [22/Apr/2025 15:42:03] "POST   _internal.py:97
                    /api/ping-service HTTP/1.1" 200 -                           
[15:42:04] INFO     Testing vLLM service at                       web_app.py:153
                    http://**************:29001 with prompt:                    
                    Hello, model_id: deepseek                                   
           INFO     Using model: deepseek                         web_app.py:161
           INFO     Testing endpoint:                             web_app.py:236
                    http://**************:29001/v1/chat/completio               
                    ns with payload type: ['messages',                          
                    'max_tokens', 'temperature']                                
           INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:29001/v1/chat/completi                
                    ons "HTTP/1.1 200 OK"                                       
           INFO     Response status: 200                          web_app.py:245
           INFO     Response: {'id':                              web_app.py:248
                    'chat-9df5ddff4aeb4043b3c25fc3753c39d0',                    
                    'object': 'chat.completion', 'created':                     
                    1745307724, 'model': 'deepseek', 'choices':                 
                    [{'index': 0, 'message': {'role':                           
                    'assistant', 'content': 'Alright',                          
                    'tool_calls': []}, 'logprobs': None,                        
                    'finish_reason': 'length', 'stop_reason':                   
                    None}], 'usage': {'prompt_tokens': 6,                       
                    'total_tokens': 7, 'completion_tokens': 1},                 
                    'prompt_logprobs': None}                                    
           INFO     Successfully tested vLLM service at           web_app.py:254
                    http://**************:29001/v1/chat/completio               
                    ns                                                          
           INFO     127.0.0.1 - - [22/Apr/2025 15:42:04] "POST   _internal.py:97
                    /api/ping-service HTTP/1.1" 200 -                           
[15:42:08] INFO     Streaming chat with vLLM service at           web_app.py:277
                    http://**************:29001 with 1 messages                 
           INFO     Using model: deepseek                         web_app.py:285
           INFO     Trying streaming endpoint:                    web_app.py:332
                    http://**************:29001/v1/chat/completio               
                    ns                                                          
           INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:29001/v1/chat/completi                
                    ons "HTTP/1.1 200 OK"                                       
[15:42:09] INFO     127.0.0.1 - - [22/Apr/2025 15:42:09] "POST   _internal.py:97
                    /api/chat HTTP/1.1" 200 -                                   
[15:42:13] INFO     Streaming chat with vLLM service at           web_app.py:277
                    http://**************:29001 with 3 messages                 
           INFO     Using model: deepseek                         web_app.py:285
           INFO     Trying streaming endpoint:                    web_app.py:332
                    http://**************:29001/v1/chat/completio               
                    ns                                                          
           INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:29001/v1/chat/completi                
                    ons "HTTP/1.1 200 OK"                                       
           INFO     127.0.0.1 - - [22/Apr/2025 15:42:13] "POST   _internal.py:97
                    /api/chat HTTP/1.1" 200 -                                   
[15:42:18] INFO     Streaming chat with vLLM service at           web_app.py:277
                    http://**************:29001 with 5 messages                 
           INFO     Using model: deepseek                         web_app.py:285
           INFO     Trying streaming endpoint:                    web_app.py:332
                    http://**************:29001/v1/chat/completio               
                    ns                                                          
           INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:29001/v1/chat/completi                
                    ons "HTTP/1.1 200 OK"                                       
           INFO     127.0.0.1 - - [22/Apr/2025 15:42:18] "POST   _internal.py:97
                    /api/chat HTTP/1.1" 200 -                                   
[17:24:47] INFO     127.0.0.1 - - [22/Apr/2025 17:24:47] "GET /  _internal.py:97
                    HTTP/1.1" 200 -                                             
[17:24:48] INFO     127.0.0.1 - - [22/Apr/2025 17:24:48] "GET    _internal.py:97
                    /host/************** HTTP/1.1" 200 -                        
[17:24:55] INFO     Testing vLLM service at                       web_app.py:153
                    http://**************:50012 with prompt:                    
                    Hello, how are you?, model_id: yi                           
           INFO     Using model: yi                               web_app.py:161
           INFO     Testing endpoint:                             web_app.py:236
                    http://**************:50012/v1/chat/completio               
                    ns with payload type: ['messages',                          
                    'max_tokens', 'temperature']                                
[17:24:56] INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:50012/v1/chat/completi                
                    ons "HTTP/1.1 200 OK"                                       
           INFO     Response status: 200                          web_app.py:245
           INFO     Response: {'id':                              web_app.py:248
                    'chat-651a12690050455fa72704b6bec5a3d5',                    
                    'object': 'chat.completion', 'created':                     
                    1745313895, 'model': 'yi', 'choices':                       
                    [{'index': 0, 'message': {'role':                           
                    'assistant', 'content': "Hello! As an                       
                    artificial intelligence, I don't have                       
                    feelings or emotions, but I'm here and ready                
                    to help you with any information or tasks you               
                    need assistance with. How can I help you                    
                    today?", 'tool_calls': []}, 'logprobs': None,               
                    'finish_reason': 'stop', 'stop_reason':                     
                    None}], 'usage': {'prompt_tokens': 35,                      
                    'total_tokens': 77, 'completion_tokens': 42},               
                    'prompt_logprobs': None}                                    
           INFO     Successfully tested vLLM service at           web_app.py:254
                    http://**************:50012/v1/chat/completio               
                    ns                                                          
           INFO     127.0.0.1 - - [22/Apr/2025 17:24:56] "POST   _internal.py:97
                    /test-service HTTP/1.1" 200 -                               
[17:24:59] INFO     127.0.0.1 - - [22/Apr/2025 17:24:59] "GET /  _internal.py:97
                    HTTP/1.1" 200 -                                             
[17:25:00] INFO     127.0.0.1 - - [22/Apr/2025 17:25:00]         _internal.py:97
                    "[32mPOST /test-service HTTP/1.1[0m" 302 -                  
           INFO     127.0.0.1 - - [22/Apr/2025 17:25:00] "GET /  _internal.py:97
                    HTTP/1.1" 200 -                                             
[17:25:02] INFO     127.0.0.1 - - [22/Apr/2025 17:25:02] "GET    _internal.py:97
                    /host/************** HTTP/1.1" 200 -                        
[17:25:05] INFO     127.0.0.1 - - [22/Apr/2025 17:25:05] "POST   _internal.py:97
                    /test-service HTTP/1.1" 200 -                               
           INFO     Testing vLLM service at                       web_app.py:153
                    http://**************:50012 with prompt:                    
                    Hello, model_id: yi                                         
           INFO     Using model: yi                               web_app.py:161
           INFO     127.0.0.1 - - [22/Apr/2025 17:25:05] "POST   _internal.py:97
                    /api/get-models HTTP/1.1" 200 -                             
           INFO     127.0.0.1 - - [22/Apr/2025 17:25:05] "GET    _internal.py:97
                    /api/get-all-services HTTP/1.1" 200 -                       
           INFO     Testing endpoint:                             web_app.py:236
                    http://**************:50012/v1/chat/completio               
                    ns with payload type: ['messages',                          
                    'max_tokens', 'temperature']                                
           INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:50012/v1/chat/completi                
                    ons "HTTP/1.1 200 OK"                                       
           INFO     Response status: 200                          web_app.py:245
           INFO     Response: {'id':                              web_app.py:248
                    'chat-537cee88478a4f45b22d50cfed6314dd',                    
                    'object': 'chat.completion', 'created':                     
                    1745313905, 'model': 'yi', 'choices':                       
                    [{'index': 0, 'message': {'role':                           
                    'assistant', 'content': 'Hello',                            
                    'tool_calls': []}, 'logprobs': None,                        
                    'finish_reason': 'length', 'stop_reason':                   
                    None}], 'usage': {'prompt_tokens': 30,                      
                    'total_tokens': 31, 'completion_tokens': 1},                
                    'prompt_logprobs': None}                                    
           INFO     Successfully tested vLLM service at           web_app.py:254
                    http://**************:50012/v1/chat/completio               
                    ns                                                          
           INFO     127.0.0.1 - - [22/Apr/2025 17:25:05] "POST   _internal.py:97
                    /api/ping-service HTTP/1.1" 200 -                           
[17:25:08] INFO     Streaming chat with vLLM service at           web_app.py:277
                    http://**************:50012 with 1 messages                 
           INFO     Using model: yi                               web_app.py:285
           INFO     Trying streaming endpoint:                    web_app.py:332
                    http://**************:50012/v1/chat/completio               
                    ns                                                          
           INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:50012/v1/chat/completi                
                    ons "HTTP/1.1 200 OK"                                       
           INFO     127.0.0.1 - - [22/Apr/2025 17:25:08] "POST   _internal.py:97
                    /api/chat HTTP/1.1" 200 -                                   
[17:25:13] INFO     Streaming chat with vLLM service at           web_app.py:277
                    http://**************:50012 with 3 messages                 
           INFO     Using model: yi                               web_app.py:285
           INFO     Trying streaming endpoint:                    web_app.py:332
                    http://**************:50012/v1/chat/completio               
                    ns                                                          
           INFO     HTTP Request: POST                           _client.py:1740
                    http://**************:50012/v1/chat/completi                
                    ons "HTTP/1.1 200 OK"                                       
           INFO     127.0.0.1 - - [22/Apr/2025 17:25:13] "POST   _internal.py:97
                    /api/chat HTTP/1.1" 200 -                                   
