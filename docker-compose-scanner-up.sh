#!/bin/bash

# Run with docker-compose using the scanner configuration
echo "Starting vLLM Detector with scanner service using docker-compose..."
docker-compose -f docker-compose.scanner.yml up -d

echo "Container started! Access the web interface at http://localhost:5000"
echo "Scanner service is running in the background"
echo "To stop the container: docker-compose -f docker-compose.scanner.yml down"
