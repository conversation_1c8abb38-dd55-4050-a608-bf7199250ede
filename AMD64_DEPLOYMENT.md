# AMD64 Docker Deployment Guide

This guide provides instructions for building an AMD64 Docker image of the vLLM Detector application and deploying it to a remote server.

## Building and Pushing the Image

### Prerequisites

- Docker installed on your build machine (version 19.03 or later)
- Docker Buildx installed and enabled (included in Docker Desktop and recent Docker Engine versions)
- Access to a Docker registry (Docker Hub, GitHub Container Registry, private registry, etc.)
- Docker installed on your target server

### Building the AMD64 Image

To build an AMD64 Docker image, use the provided script:

```bash
./docker-build-amd64.sh --tag <tag> --registry <registry> --name <image-name>
```

Example:
```bash
./docker-build-amd64.sh --tag v1.0 --registry myregistry.example.com --name vllm-detector
```

This will build an image tagged as `myregistry.example.com/vllm-detector:v1.0`.

### Pushing the Image to a Registry

After building the image, you can push it to your Docker registry:

```bash
./docker-push.sh --tag <tag> --registry <registry> --name <image-name>
```

Example:
```bash
./docker-push.sh --tag v1.0 --registry myregistry.example.com --name vllm-detector
```

### Building and Pushing in One Step

For convenience, you can build and push the image in one step:

```bash
./docker-build-and-push.sh --tag <tag> --registry <registry> --name <image-name>
```

Example:
```bash
./docker-build-and-push.sh --tag v1.0 --registry myregistry.example.com --name vllm-detector
```

## Deploying to a Remote Server

### Prerequisites

- Docker and Docker Compose installed on your target server
- Access to pull the image from your Docker registry

### Deployment Files

Copy the following files to your target server:

- `docker-compose.remote.yml` - For deploying just the web application
- `docker-compose.remote.scanner.yml` - For deploying the web application with the scanner service
- `remote-deploy.sh` - Script to simplify deployment

### Deploying the Application

On your target server, run:

```bash
./remote-deploy.sh --tag <tag> --registry <registry> --name <image-name>
```

Example:
```bash
./remote-deploy.sh --tag v1.0 --registry myregistry.example.com --name vllm-detector
```

This will deploy just the web application.

### Deploying with Scanner Service

To deploy with the scanner service enabled:

```bash
./remote-deploy.sh --tag <tag> --registry <registry> --name <image-name> --scanner
```

Example:
```bash
./remote-deploy.sh --tag v1.0 --registry myregistry.example.com --name vllm-detector --scanner
```

### Configuration

You can configure the application by editing the environment variables in the docker-compose files:

#### Web Application

- `WEB_PORT`: Port for the web application (default: 5000)
- `SECRET_KEY`: Flask secret key (default: "vllm-detector-secret-key")

#### Scanner Service

- `SCAN_ENABLED`: Enable or disable the scanner service (default: "false")
- `SCAN_HOST`: Host to scan for vLLM services (default: "localhost")
- `SCAN_PORT_MIN`: Minimum port to scan (default: 8000)
- `SCAN_PORT_MAX`: Maximum port to scan (default: 9000)
- `SCAN_CONCURRENCY`: Number of concurrent scans (default: 100)

### Stopping the Application

To stop the application:

```bash
docker-compose -f docker-compose.remote.yml down
```

Or if you deployed with the scanner service:

```bash
docker-compose -f docker-compose.remote.scanner.yml down
```

## Troubleshooting

### Image Pull Issues

If you have trouble pulling the image, make sure:

1. Your target server has access to the Docker registry
2. You're logged in to the registry if it's private:
   ```bash
   docker login <registry>
   ```

### Container Fails to Start

If the container fails to start, check the logs:

```bash
docker-compose -f docker-compose.remote.yml logs
```

### Cannot Access Web Application

If you cannot access the web application, check that:

1. The container is running: `docker ps`
2. The port is correctly mapped: `docker port <container-id>`
3. No firewall is blocking access to port 5000

### Buildx Issues

If you encounter issues with Docker Buildx:

1. Make sure you have Docker 19.03 or later: `docker --version`
2. Check if Buildx is installed: `docker buildx version`
3. If Buildx is not available, you can install it following the [official documentation](https://docs.docker.com/buildx/working-with-buildx/)
4. On some systems, you may need to enable experimental features in Docker

### Architecture Verification

To verify the architecture of your built image:

```bash
docker inspect <image-name> | grep Architecture
```

This should output `"Architecture": "amd64"` for an AMD64 image.
