#!/bin/bash
# Stop the vLLM detector web application

# Default cache directory
CACHE_DIR=".cache"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --cache-dir)
      CACHE_DIR="$2"
      shift 2
      ;;
    --help)
      echo "Usage: $0 [options]"
      echo "Options:"
      echo "  --cache-dir DIR      Directory where cache files are stored (default: .cache)"
      echo "  --help               Show this help message and exit"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Check if PID file exists
PID_FILE="$CACHE_DIR/vllm_web_app.pid"
if [ ! -f "$PID_FILE" ]; then
  echo "PID file not found at $PID_FILE"
  echo "Web application may not be running or was started with a different cache directory"
  exit 1
fi

# Read PID from file
PID=$(cat "$PID_FILE")
if [ -z "$PID" ]; then
  echo "PID file is empty"
  exit 1
fi

# Check if process is running
if ! ps -p "$PID" > /dev/null; then
  echo "Process with PID $PID is not running"
  rm -f "$PID_FILE"
  exit 1
fi

# Stop the process
echo "Stopping vLLM detector web application with PID $PID..."
kill "$PID"

# Wait for process to terminate
for i in {1..10}; do
  if ! ps -p "$PID" > /dev/null; then
    echo "Web application stopped successfully"
    rm -f "$PID_FILE"
    exit 0
  fi
  echo "Waiting for web application to stop... ($i/10)"
  sleep 1
done

# Force kill if still running
echo "Web application did not stop gracefully, force killing..."
kill -9 "$PID"
if ! ps -p "$PID" > /dev/null; then
  echo "Web application force stopped successfully"
  rm -f "$PID_FILE"
  exit 0
else
  echo "Failed to stop web application with PID $PID"
  exit 1
fi
