#!/usr/bin/env python
"""Example script to scan for vLLM services on a host by checking all ports."""

import argparse
import asyncio
import logging
import sys

from rich.logging import <PERSON><PERSON><PERSON><PERSON>

from vllm_detector.detector import detect_servers_with_models

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(rich_tracebacks=True)],
)
logger = logging.getLogger("vllm_detector")


async def main():
    """Scan for vLLM services on a host."""
    parser = argparse.ArgumentParser(description="Scan for vLLM services on a host")
    parser.add_argument("host", help="Hostname or IP address to scan")
    parser.add_argument("--port-min", type=int, default=1, help="Minimum port to scan")
    parser.add_argument("--port-max", type=int, default=65535, help="Maximum port to scan")
    parser.add_argument("--timeout", type=int, default=5, help="Request timeout in seconds")
    parser.add_argument("--scan-all", action="store_true", help="Scan all ports even after finding a server")
    parser.add_argument("--scan-all-ports", action="store_true", default=True, help="Scan all ports even if common ports are open")
    parser.add_argument("--concurrency", "-c", type=int, default=500, help="Number of ports to scan concurrently")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")

    args = parser.parse_args()

    if args.verbose:
        logger.setLevel(logging.DEBUG)

    logger.info(f"Scanning {args.host} for vLLM services (ports {args.port_min}-{args.port_max})...")

    try:
        servers = await detect_servers_with_models(
            host=args.host,
            port_range=(args.port_min, args.port_max),
            timeout=args.timeout,
            scan_all=args.scan_all,
            scan_all_ports=args.scan_all_ports,
            concurrency=args.concurrency,
        )

        if not servers:
            logger.info("No vLLM services detected.")
            return

        logger.info(f"Found {len(servers)} vLLM services:")
        for server in servers:
            logger.info(f"- {server.url}")
            if server.models:
                logger.info(f"  Models: {', '.join([model.model_id for model in server.models])}")
                for model in server.models:
                    logger.info(f"    - {model.model_id}")
                    if model.max_model_len:
                        logger.info(f"      Max length: {model.max_model_len}")
                    if model.dtype:
                        logger.info(f"      Data type: {model.dtype}")
                    if model.device_type:
                        logger.info(f"      Device: {model.device_type}")

    except Exception as e:
        logger.error(f"Error: {e}")
        if args.verbose:
            import traceback
            logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
