#!/usr/bin/env python
"""Service script to periodically scan for vLLM services on a host."""

import argparse
import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

from rich.logging import <PERSON><PERSON><PERSON><PERSON>

from vllm_detector.detector import detect_servers_with_models, scan_ports

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(rich_tracebacks=True)],
)
logger = logging.getLogger("vllm_detector")


class VLLMScannerService:
    """Service to periodically scan for vLLM services on a host."""

    def __init__(
        self,
        host: str,
        port_range: Tuple[int, int] = (1, 65535),
        timeout: int = 5,
        scan_all: bool = True,
        scan_all_ports: bool = True,
        concurrency: int = 500,
        cache_dir: str = ".cache",
        verbose: bool = False,
    ):
        """Initialize the service.

        Args:
            host: Hostname or IP address to scan
            port_range: Range of ports to scan (min, max)
            timeout: Request timeout in seconds
            scan_all: Whether to scan all ports in range or stop after finding one server
            scan_all_ports: Whether to scan all ports even if common ports are open
            concurrency: Number of ports to scan concurrently
            cache_dir: Directory to store cache files
            verbose: Whether to enable verbose logging
        """
        self.host = host
        self.port_range = port_range
        self.timeout = timeout
        self.scan_all = scan_all
        self.scan_all_ports = scan_all_ports
        self.concurrency = concurrency
        self.cache_dir = Path(cache_dir)
        self.verbose = verbose

        # Create cache directory if it doesn't exist
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        # Set up logging
        if verbose:
            logger.setLevel(logging.DEBUG)

        # Cache file paths
        self.open_ports_cache_file = self.cache_dir / f"open_ports_{host}.json"
        self.services_cache_file = self.cache_dir / f"services_{host}.json"

    async def scan_open_ports(self) -> List[int]:
        """Scan for open ports on the host.

        Returns:
            List of open ports
        """
        logger.info(f"Scanning {self.host} for open ports (range {self.port_range[0]}-{self.port_range[1]})...")
        open_ports = await scan_ports(
            host=self.host,
            port_range=self.port_range,
            scan_all_ports=self.scan_all_ports,
            concurrency=self.concurrency,
        )
        logger.info(f"Found {len(open_ports)} open ports on {self.host}: {open_ports}")
        return open_ports

    async def scan_vllm_services(self, open_ports: Optional[List[int]] = None) -> Dict:
        """Scan for vLLM services on the host.

        Args:
            open_ports: List of open ports to check. If None, will use cached ports or scan.

        Returns:
            Dictionary with service information
        """
        # If no open ports provided, try to load from cache or scan
        if open_ports is None:
            open_ports = self.load_open_ports_cache()
            if not open_ports:
                open_ports = await self.scan_open_ports()
                self.save_open_ports_cache(open_ports)

        logger.info(f"Scanning {self.host} for vLLM services on {len(open_ports)} open ports...")
        servers = await detect_servers_with_models(
            host=self.host,
            port_range=self.port_range,
            timeout=self.timeout,
            scan_all=self.scan_all,
            scan_all_ports=self.scan_all_ports,
            concurrency=self.concurrency,
            open_ports=open_ports,  # Pass open ports to avoid rescanning
        )

        # Convert servers to a serializable format
        services_data = {
            "timestamp": datetime.now().isoformat(),
            "host": self.host,
            "servers": [],
        }

        if servers:
            logger.info(f"Found {len(servers)} vLLM services:")
            for server in servers:
                server_data = {
                    "url": server.url,
                    "port": server.port,
                    "models": [],
                }
                if server.models:
                    logger.info(f"- {server.url}")
                    logger.info(f"  Models: {', '.join([model.model_id for model in server.models])}")
                    for model in server.models:
                        model_data = {
                            "model_id": model.model_id,
                            "max_model_len": model.max_model_len,
                            "dtype": model.dtype,
                            "device_type": model.device_type,
                        }
                        server_data["models"].append(model_data)
                        logger.info(f"    - {model.model_id}")
                        if model.max_model_len:
                            logger.info(f"      Max length: {model.max_model_len}")
                        if model.dtype:
                            logger.info(f"      Data type: {model.dtype}")
                        if model.device_type:
                            logger.info(f"      Device: {model.device_type}")
                services_data["servers"].append(server_data)
        else:
            logger.info("No vLLM services detected.")

        # Save services data to cache
        self.save_services_cache(services_data)

        return services_data

    def load_open_ports_cache(self) -> List[int]:
        """Load open ports from cache file.

        Returns:
            List of open ports
        """
        if not self.open_ports_cache_file.exists():
            logger.info(f"No open ports cache file found at {self.open_ports_cache_file}")
            return []

        try:
            with open(self.open_ports_cache_file, "r") as f:
                cache_data = json.load(f)
                timestamp = datetime.fromisoformat(cache_data["timestamp"])
                # Check if cache is from today
                if timestamp.date() != datetime.now().date():
                    logger.info(f"Open ports cache is from {timestamp.date()}, refreshing...")
                    return []
                logger.info(f"Loaded {len(cache_data['open_ports'])} open ports from cache (from {timestamp})")
                return cache_data["open_ports"]
        except Exception as e:
            logger.error(f"Error loading open ports cache: {e}")
            return []

    def save_open_ports_cache(self, open_ports: List[int]) -> None:
        """Save open ports to cache file.

        Args:
            open_ports: List of open ports
        """
        cache_data = {
            "timestamp": datetime.now().isoformat(),
            "host": self.host,
            "open_ports": open_ports,
        }
        try:
            with open(self.open_ports_cache_file, "w") as f:
                json.dump(cache_data, f, indent=2)
            logger.info(f"Saved {len(open_ports)} open ports to cache at {self.open_ports_cache_file}")
        except Exception as e:
            logger.error(f"Error saving open ports cache: {e}")

    def load_services_cache(self) -> Dict:
        """Load services from cache file.

        Returns:
            Dictionary with service information
        """
        if not self.services_cache_file.exists():
            logger.info(f"No services cache file found at {self.services_cache_file}")
            return {}

        try:
            with open(self.services_cache_file, "r") as f:
                cache_data = json.load(f)
                timestamp = datetime.fromisoformat(cache_data["timestamp"])
                logger.info(f"Loaded services cache from {timestamp}")
                return cache_data
        except Exception as e:
            logger.error(f"Error loading services cache: {e}")
            return {}

    def save_services_cache(self, services_data: Dict) -> None:
        """Save services to cache file.

        Args:
            services_data: Dictionary with service information
        """
        try:
            with open(self.services_cache_file, "w") as f:
                json.dump(services_data, f, indent=2)
            logger.info(f"Saved services data to cache at {self.services_cache_file}")
        except Exception as e:
            logger.error(f"Error saving services cache: {e}")

    def is_workday(self) -> bool:
        """Check if today is a workday (Monday to Friday).

        Returns:
            True if today is a workday, False otherwise
        """
        return datetime.now().weekday() < 5  # 0-4 are Monday to Friday

    def is_work_hours(self) -> bool:
        """Check if current time is within work hours (9 AM to 9 PM).

        Returns:
            True if current time is within work hours, False otherwise
        """
        current_hour = datetime.now().hour
        return 9 <= current_hour < 21  # 9 AM to 9 PM

    def should_refresh_ports(self) -> bool:
        """Check if open ports cache should be refreshed.

        Returns:
            True if open ports cache should be refreshed, False otherwise
        """
        # Refresh ports at midnight (between 0:00 and 1:00)
        current_hour = datetime.now().hour
        return current_hour < 1

    def should_scan_services(self) -> bool:
        """Check if services should be scanned.

        Returns:
            True if services should be scanned, False otherwise
        """
        # Scan services during work hours on workdays
        return self.is_workday() and self.is_work_hours()

    async def run_service(self) -> None:
        """Run the service loop."""
        logger.info(f"Starting vLLM scanner service for {self.host}")
        logger.info(f"Cache directory: {self.cache_dir}")
        logger.info(f"Port range: {self.port_range[0]}-{self.port_range[1]}")
        logger.info(f"Concurrency: {self.concurrency}")

        # Initial scan
        open_ports = self.load_open_ports_cache()
        if not open_ports:
            open_ports = await self.scan_open_ports()
            self.save_open_ports_cache(open_ports)

        await self.scan_vllm_services(open_ports)

        # Service loop
        while True:
            try:
                # Sleep for 30 minutes
                logger.info("Sleeping for 30 minutes...")
                await asyncio.sleep(30 * 60)

                # Check if we should refresh ports
                if self.should_refresh_ports():
                    logger.info("Refreshing open ports cache...")
                    open_ports = await self.scan_open_ports()
                    self.save_open_ports_cache(open_ports)

                # Check if we should scan services
                if self.should_scan_services():
                    logger.info("Scanning for vLLM services during work hours...")
                    await self.scan_vllm_services(open_ports)
                else:
                    logger.info("Not scanning for services outside of work hours")

            except Exception as e:
                logger.error(f"Error in service loop: {e}")
                if self.verbose:
                    import traceback
                    logger.error(traceback.format_exc())
                # Sleep for 5 minutes before retrying
                await asyncio.sleep(5 * 60)


async def main():
    """Run the vLLM scanner service."""
    parser = argparse.ArgumentParser(description="Service to periodically scan for vLLM services on a host")
    parser.add_argument("host", help="Hostname or IP address to scan")
    parser.add_argument("--port-min", type=int, default=1, help="Minimum port to scan")
    parser.add_argument("--port-max", type=int, default=65535, help="Maximum port to scan")
    parser.add_argument("--timeout", type=int, default=5, help="Request timeout in seconds")
    parser.add_argument("--scan-all", action="store_true", help="Scan all ports even after finding a server")
    parser.add_argument("--scan-all-ports", action="store_true", default=True, help="Scan all ports even if common ports are open")
    parser.add_argument("--concurrency", "-c", type=int, default=500, help="Number of ports to scan concurrently")
    parser.add_argument("--cache-dir", default=".cache", help="Directory to store cache files")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")

    args = parser.parse_args()

    service = VLLMScannerService(
        host=args.host,
        port_range=(args.port_min, args.port_max),
        timeout=args.timeout,
        scan_all=args.scan_all,
        scan_all_ports=args.scan_all_ports,
        concurrency=args.concurrency,
        cache_dir=args.cache_dir,
        verbose=args.verbose,
    )

    try:
        await service.run_service()
    except KeyboardInterrupt:
        logger.info("Service stopped by user")
    except Exception as e:
        logger.error(f"Service error: {e}")
        if args.verbose:
            import traceback
            logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
