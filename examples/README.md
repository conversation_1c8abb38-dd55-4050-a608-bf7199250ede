# vLLM Detector Examples

This directory contains example scripts for using the vLLM detector.

## Scan Ports

The `scan_ports.py` script is a simple example of how to use the vLLM detector to scan for vLLM services on a host.

```bash
python examples/scan_ports.py <host> --port-min <min_port> --port-max <max_port> --scan-all --concurrency <num_concurrent> --verbose
```

## Scanner Service

The `service.py` script is a service that periodically scans for vLLM services on a host. It caches open ports and services information, and refreshes them periodically.

- Scans for open ports at midnight every day
- Scans for vLLM services every 30 minutes during work hours (9 AM to 9 PM) on workdays (Monday to Friday)

### Starting the Service

```bash
./examples/start_service.sh --host <host> --port-min <min_port> --port-max <max_port> --concurrency <num_concurrent>
```

### Stopping the Service

```bash
./examples/stop_service.sh
```

## Web Application

The `web_app.py` script is a web application that displays the cached open ports and vLLM services information. It also provides a feature to test vLLM services by sending prompts to them.

### Starting the Web Application

```bash
./examples/start_web_app.sh --host <host> --port <port> --cache-dir <cache_dir>
```

### Required Dependencies

The web application requires the following dependencies:

- Flask with async support (`flask[async]`)
- httpx (for making HTTP requests)
- asyncio (for async/await support and event loops)

These dependencies will be automatically installed by the `start_web_app.sh` script if they are not already installed.

### Stopping the Web Application

```bash
./examples/stop_web_app.sh
```

### Accessing the Web Application

Once the web application is running, you can access it at `http://<host>:<port>`.

### Features

- **Modern UI**: The web application uses Tailwind CSS for a modern and responsive user interface.
- **Host List**: View a list of all monitored hosts.
- **Open Ports**: View a list of open ports for each host.
- **vLLM Services**: View a list of vLLM services and their models for each host.
- **Service Testing**: Test vLLM services by sending prompts to them and viewing the responses.
- **Multiple API Format Support**: Supports both OpenAI Completions API and Chat Completions API formats, as well as vLLM's native formats.
- **Model Selection**: Automatically uses the detected model IDs from the service, or allows manual specification of model ID. Supports dynamic model switching in the chat interface.
- **Service URL Switching**: Allows changing the service URL directly in the chat interface without starting a new session. Supports both dropdown selection from available services and manual URL entry.
- **Custom Prompts**: Provides preset prompts and allows users to enter custom prompts for testing.
- **Chat Interface**: Interactive chat interface with streaming responses for multi-turn conversations.
- **Streaming Responses**: Real-time streaming of model responses in chat mode.
- **Async Support**: Uses Flask's async support for non-blocking API calls to vLLM services.

## Complete Workflow

1. Start the scanner service to scan for open ports and vLLM services:

```bash
./examples/start_service.sh --host ************** --port-min 8000 --port-max 9100 --concurrency 1000
```

2. Start the web application to view the results:

```bash
./examples/start_web_app.sh --port 5000
```

3. Access the web application at `http://localhost:5000`

4. When you're done, stop the services:

```bash
./examples/stop_service.sh
./examples/stop_web_app.sh
```

## Docker Deployment

You can also deploy the web application using Docker. This is the recommended approach for production deployments.

### Using Docker Compose (Recommended)

#### Web Application Only

1. Build and start the container with just the web application:

```bash
./docker-compose-up.sh
```

2. Access the web application at `http://localhost:5000`

3. To stop the container:

```bash
docker-compose down
```

#### Web Application with Scanner Service

1. Build and start the container with both the web application and scanner service:

```bash
./docker-compose-scanner-up.sh
```

2. Access the web application at `http://localhost:5000`

3. To stop the container:

```bash
docker-compose -f docker-compose.scanner.yml down
```

### Using Docker Directly

1. Build the Docker image:

```bash
./docker-build.sh
```

2. Run the container:

```bash
./docker-run.sh
```

3. Access the web application at `http://localhost:5000`

For more detailed instructions on Docker deployment, see [DOCKER.md](../DOCKER.md).
