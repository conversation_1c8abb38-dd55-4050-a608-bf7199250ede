#!/bin/bash
# Start the vLLM scanner service as a background process

# Default values
HOST="localhost"
PORT_MIN=1
PORT_MAX=65535
CONCURRENCY=500
CACHE_DIR=".cache"
LOG_FILE="vllm_scanner.log"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --host)
      HOST="$2"
      shift 2
      ;;
    --port-min)
      PORT_MIN="$2"
      shift 2
      ;;
    --port-max)
      PORT_MAX="$2"
      shift 2
      ;;
    --concurrency)
      CONCURRENCY="$2"
      shift 2
      ;;
    --cache-dir)
      CACHE_DIR="$2"
      shift 2
      ;;
    --log-file)
      LOG_FILE="$2"
      shift 2
      ;;
    --help)
      echo "Usage: $0 [options]"
      echo "Options:"
      echo "  --host HOST          Hostname or IP address to scan (default: localhost)"
      echo "  --port-min MIN       Minimum port to scan (default: 1)"
      echo "  --port-max MAX       Maximum port to scan (default: 65535)"
      echo "  --concurrency N      Number of ports to scan concurrently (default: 500)"
      echo "  --cache-dir DIR      Directory to store cache files (default: .cache)"
      echo "  --log-file FILE      Log file path (default: vllm_scanner.log)"
      echo "  --help               Show this help message and exit"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Ensure the cache directory exists
mkdir -p "$CACHE_DIR"

# Start the service in the background
echo "Starting vLLM scanner service for $HOST (ports $PORT_MIN-$PORT_MAX)..."
echo "Logs will be written to $LOG_FILE"
echo "Cache files will be stored in $CACHE_DIR"

# Activate virtual environment if it exists
if [ -d ".venv" ]; then
  source .venv/bin/activate
fi

# Start the service
nohup python examples/service.py "$HOST" \
  --port-min "$PORT_MIN" \
  --port-max "$PORT_MAX" \
  --concurrency "$CONCURRENCY" \
  --cache-dir "$CACHE_DIR" \
  --scan-all \
  --verbose > "$LOG_FILE" 2>&1 &

# Save the process ID
PID=$!
echo "$PID" > "$CACHE_DIR/vllm_scanner.pid"
echo "Service started with PID $PID"
echo "To stop the service, run: kill $(cat $CACHE_DIR/vllm_scanner.pid)"
