#!/bin/bash
# Start the vLLM detector web application

# Default values
HOST="0.0.0.0"
PORT=5000
CACHE_DIR=".cache"
LOG_FILE="vllm_web_app.log"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --host)
      HOST="$2"
      shift 2
      ;;
    --port)
      PORT="$2"
      shift 2
      ;;
    --cache-dir)
      CACHE_DIR="$2"
      shift 2
      ;;
    --log-file)
      LOG_FILE="$2"
      shift 2
      ;;
    --help)
      echo "Usage: $0 [options]"
      echo "Options:"
      echo "  --host HOST          Host to bind to (default: 0.0.0.0)"
      echo "  --port PORT          Port to bind to (default: 5000)"
      echo "  --cache-dir DIR      Directory where cache files are stored (default: .cache)"
      echo "  --log-file FILE      Log file path (default: vllm_web_app.log)"
      echo "  --help               Show this help message and exit"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Ensure the cache directory exists
mkdir -p "$CACHE_DIR"

# Start the web application in the background
echo "Starting vLLM detector web application on $HOST:$PORT..."
echo "Logs will be written to $LOG_FILE"
echo "Cache files will be read from $CACHE_DIR"

# Activate virtual environment if it exists
if [ -d ".venv" ]; then
  source .venv/bin/activate
fi

# Install required dependencies if not already installed
if ! python -c "import flask" &> /dev/null; then
  echo "Installing Flask with async support..."
  pip install "flask[async]"
fi

if ! python -c "import httpx" &> /dev/null; then
  echo "Installing httpx..."
  pip install httpx
fi

# asyncio is part of the Python standard library, no need to install it

# Start the web application
nohup python examples/web_app.py \
  --cache-dir "$CACHE_DIR" \
  --host "$HOST" \
  --port "$PORT" > "$LOG_FILE" 2>&1 &

# Save the process ID
PID=$!
echo "$PID" > "$CACHE_DIR/vllm_web_app.pid"
echo "Web application started with PID $PID"
echo "To stop the web application, run: kill $(cat $CACHE_DIR/vllm_web_app.pid)"
echo "Access the web application at http://$HOST:$PORT"
