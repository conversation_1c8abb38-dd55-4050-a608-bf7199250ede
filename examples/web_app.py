#!/usr/bin/env python
"""Web application to display vLLM services and open ports."""

import argparse
import asyncio
import json
import logging
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, AsyncGenerator, Any

import httpx
from flask import Flask, render_template, jsonify, request, redirect, url_for, flash, Response

# Set up httpx logging
logging.getLogger("httpx").setLevel(logging.INFO)
from rich.logging import RichHandler

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(rich_tracebacks=True)],
)
logger = logging.getLogger("vllm_detector_web")

app = Flask(__name__, template_folder="web_templates")
app.secret_key = os.environ.get("SECRET_KEY", "vllm-detector-secret-key")

# Add custom filters
@app.template_filter('tojson')
def tojson_filter(obj, indent=None):
    return json.dumps(obj, indent=indent)

@app.template_filter('now')
def now_filter(format_string):
    return datetime.now().strftime(format_string)


class VLLMWebApp:
    """Web application to display vLLM services and open ports."""

    def __init__(self, cache_dir: str = ".cache"):
        """Initialize the web application.

        Args:
            cache_dir: Directory where cache files are stored
        """
        self.cache_dir = Path(cache_dir)
        self.hosts = self._discover_hosts()

    def _discover_hosts(self) -> List[str]:
        """Discover hosts from cache files.

        Returns:
            List of host names
        """
        hosts = set()
        if self.cache_dir.exists():
            for file in self.cache_dir.glob("open_ports_*.json"):
                host = file.name.replace("open_ports_", "").replace(".json", "")
                hosts.add(host)
            for file in self.cache_dir.glob("services_*.json"):
                host = file.name.replace("services_", "").replace(".json", "")
                hosts.add(host)
        return sorted(list(hosts))

    def get_open_ports(self, host: str) -> Dict:
        """Get open ports for a host.

        Args:
            host: Hostname or IP address

        Returns:
            Dictionary with open ports information
        """
        cache_file = self.cache_dir / f"open_ports_{host}.json"
        if not cache_file.exists():
            return {
                "timestamp": None,
                "host": host,
                "open_ports": [],
                "error": "No cache file found"
            }

        try:
            with open(cache_file, "r") as f:
                data = json.load(f)
                # Convert timestamp to readable format
                if "timestamp" in data:
                    timestamp = datetime.fromisoformat(data["timestamp"])
                    data["timestamp_readable"] = timestamp.strftime("%Y-%m-%d %H:%M:%S")
                return data
        except Exception as e:
            logger.error(f"Error loading open ports cache for {host}: {e}")
            return {
                "timestamp": None,
                "host": host,
                "open_ports": [],
                "error": str(e)
            }

    def get_services(self, host: str) -> Dict:
        """Get vLLM services for a host.

        Args:
            host: Hostname or IP address

        Returns:
            Dictionary with services information
        """
        cache_file = self.cache_dir / f"services_{host}.json"
        if not cache_file.exists():
            return {
                "timestamp": None,
                "host": host,
                "servers": [],
                "error": "No cache file found"
            }

        try:
            with open(cache_file, "r") as f:
                data = json.load(f)
                # Convert timestamp to readable format
                if "timestamp" in data:
                    timestamp = datetime.fromisoformat(data["timestamp"])
                    data["timestamp_readable"] = timestamp.strftime("%Y-%m-%d %H:%M:%S")
                return data
        except Exception as e:
            logger.error(f"Error loading services cache for {host}: {e}")
            return {
                "timestamp": None,
                "host": host,
                "servers": [],
                "error": str(e)
            }

    async def test_vllm_service(self, url: str, prompt: str = "Hello, how are you?", model_id: str = None, max_tokens: int = 100) -> Dict:
        """Test a vLLM service by sending a prompt.

        Args:
            url: URL of the vLLM service
            prompt: Prompt to send to the service
            model_id: Model ID to use (optional)
            max_tokens: Maximum number of tokens to generate (default: 100)

        Returns:
            Dictionary with test results
        """
        logger.info(f"Testing vLLM service at {url} with prompt: {prompt}, model_id: {model_id}")

        # Add http:// prefix if not present
        if not url.startswith("http"):
            url = f"http://{url}"

        # Use provided model_id or fallback to a default
        model_to_use = model_id if model_id else "default"
        logger.info(f"Using model: {model_to_use}")

        # Try different endpoints with appropriate payloads
        endpoint_configs = [
            # OpenAI Chat Completions API
            {
                "endpoint": "/v1/chat/completions",
                "payload": {
                    "messages": [
                        {"role": "user", "content": prompt}
                    ],
                    "max_tokens": max_tokens,
                    "temperature": 0.7,
                    "model": model_to_use,
                }
            },
            # OpenAI Completions API
            {
                "endpoint": "/v1/completions",
                "payload": {
                    "prompt": prompt,
                    "max_tokens": max_tokens,
                    "temperature": 0.7,
                    "model": model_to_use,
                }
            },
            # vLLM default
            {
                "endpoint": "/generate",
                "payload": {
                    "prompt": prompt,
                    "max_tokens": max_tokens,
                    "temperature": 0.7,
                    "model": model_to_use,
                }
            },
            # Common alternative
            {
                "endpoint": "/api/generate",
                "payload": {
                    "prompt": prompt,
                    "max_tokens": max_tokens,
                    "temperature": 0.7,
                    "model": model_to_use,
                }
            },
            # Root endpoint
            {
                "endpoint": "/",
                "payload": {
                    "prompt": prompt,
                    "max_tokens": max_tokens,
                    "temperature": 0.7,
                    "model": model_to_use,
                }
            }
        ]

        results = {
            "success": False,
            "url": url,
            "prompt": prompt,
            "response": None,
            "error": None,
            "endpoint": None,
            "payload": None,
            "latency": None,
        }

        async with httpx.AsyncClient(timeout=10.0) as client:
            for config in endpoint_configs:
                endpoint = config["endpoint"]
                payload = config["payload"]
                try:
                    start_time = datetime.now()
                    logger.info(f"Testing endpoint: {url}{endpoint} with payload type: {list(payload.keys())[:3]}")
                    response = await client.post(
                        f"{url}{endpoint}",
                        json=payload,
                        headers={"Content-Type": "application/json"},
                    )
                    end_time = datetime.now()
                    latency = (end_time - start_time).total_seconds()

                    logger.info(f"Response status: {response.status_code}")
                    if response.status_code == 200:
                        response_json = response.json()
                        logger.info(f"Response: {response_json}")
                        results["success"] = True
                        results["response"] = response_json
                        results["endpoint"] = endpoint
                        results["payload"] = payload
                        results["latency"] = latency
                        logger.info(f"Successfully tested vLLM service at {url}{endpoint}")
                        return results
                    else:
                        logger.info(f"Endpoint {url}{endpoint} returned status {response.status_code}: {response.text[:100]}")
                except Exception as e:
                    logger.info(f"Error testing vLLM service at {url}{endpoint}: {e}")
                    continue

        results["error"] = "Failed to connect to any endpoint"
        logger.error(f"Failed to test vLLM service at {url}")
        return results

    async def stream_chat(self, url: str, messages: List[Dict], model_id: str = None) -> AsyncGenerator[str, None]:
        """Stream chat with a vLLM service.

        Args:
            url: URL of the vLLM service
            messages: List of messages in the conversation
            model_id: Model ID to use (optional)

        Yields:
            Chunks of the generated text
        """
        logger.info(f"Streaming chat with vLLM service at {url} with {len(messages)} messages")

        # Add http:// prefix if not present
        if not url.startswith("http"):
            url = f"http://{url}"

        # Use provided model_id or fallback to a default
        model_to_use = model_id if model_id else "default"
        logger.info(f"Using model: {model_to_use}")

        # Extract the last user message
        last_user_message = ""
        for msg in reversed(messages):
            if msg.get("role") == "user":
                last_user_message = msg.get("content", "")
                break

        # Prepare the request payload for different API formats
        chat_payload = {
            "messages": messages,
            "model": model_to_use,
            "stream": True,
            "temperature": 0.7,
            "max_tokens": 500
        }

        completion_payload = {
            "prompt": last_user_message,
            "model": model_to_use,
            "stream": True,
            "temperature": 0.7,
            "max_tokens": 500
        }

        # Try different endpoints
        endpoints = [
            {
                "endpoint": "/v1/chat/completions",
                "payload": chat_payload,
                "parser": self._parse_chat_completion_chunk
            },
            {
                "endpoint": "/v1/completions",
                "payload": completion_payload,
                "parser": self._parse_completion_chunk
            }
        ]

        # Try each endpoint
        for config in endpoints:
            endpoint = config["endpoint"]
            payload = config["payload"]
            parser = config["parser"]

            try:
                logger.info(f"Trying streaming endpoint: {url}{endpoint}")
                async with httpx.AsyncClient(timeout=30.0) as client:
                    async with client.stream(
                        "POST",
                        f"{url}{endpoint}",
                        json=payload,
                        headers={"Content-Type": "application/json"},
                    ) as response:
                        if response.status_code != 200:
                            logger.warning(f"Endpoint {url}{endpoint} returned status {response.status_code}")
                            continue

                        # Process the streaming response
                        buffer = ""
                        current_text = ""

                        async for chunk in response.aiter_bytes():
                            buffer += chunk.decode("utf-8")

                            # Process complete lines
                            lines = buffer.split("\n")
                            buffer = lines.pop()  # Keep the last incomplete line in the buffer

                            for line in lines:
                                line = line.strip()
                                if not line or line == "data: [DONE]":
                                    continue

                                if line.startswith("data: "):
                                    line = line[6:]  # Remove "data: " prefix

                                try:
                                    chunk_text = parser(line)
                                    if chunk_text is not None:
                                        current_text += chunk_text
                                        yield current_text
                                except Exception as e:
                                    logger.error(f"Error parsing chunk: {e}")

                        # Return the final text if we got here
                        return
            except Exception as e:
                logger.error(f"Error streaming from {url}{endpoint}: {e}")
                continue

        # If we get here, all endpoints failed
        yield "Sorry, I couldn't connect to the service. Please try again later."

    def _parse_chat_completion_chunk(self, chunk_data: str) -> Optional[str]:
        """Parse a chunk from the chat completions API.

        Args:
            chunk_data: JSON string containing the chunk data

        Returns:
            The text content of the chunk, or None if no content
        """
        try:
            data = json.loads(chunk_data)
            if "choices" in data and len(data["choices"]) > 0:
                choice = data["choices"][0]
                if "delta" in choice and "content" in choice["delta"]:
                    return choice["delta"]["content"]
            return None
        except Exception as e:
            logger.error(f"Error parsing chat completion chunk: {e}")
            return None

    def _parse_completion_chunk(self, chunk_data: str) -> Optional[str]:
        """Parse a chunk from the completions API.

        Args:
            chunk_data: JSON string containing the chunk data

        Returns:
            The text content of the chunk, or None if no content
        """
        try:
            data = json.loads(chunk_data)
            if "choices" in data and len(data["choices"]) > 0:
                choice = data["choices"][0]
                if "text" in choice:
                    return choice["text"]
            return None
        except Exception as e:
            logger.error(f"Error parsing completion chunk: {e}")
            return None


# Create web app instance
web_app = None


@app.route("/")
def index():
    """Render the index page."""
    hosts = web_app.hosts
    return render_template("index_tailwind.html", hosts=hosts)


@app.route("/host/<host>")
def host_details(host):
    """Render the host details page."""
    open_ports = web_app.get_open_ports(host)
    services = web_app.get_services(host)
    return render_template("host_tailwind.html", host=host, open_ports=open_ports, services=services)


@app.route("/api/hosts")
def api_hosts():
    """API endpoint to get hosts."""
    return jsonify({"hosts": web_app.hosts})


@app.route("/api/host/<host>/open_ports")
def api_open_ports(host):
    """API endpoint to get open ports for a host."""
    return jsonify(web_app.get_open_ports(host))


@app.route("/api/host/<host>/services")
def api_services(host):
    """API endpoint to get services for a host."""
    return jsonify(web_app.get_services(host))


@app.route("/test-service", methods=["POST"])
async def test_service():
    """Test a vLLM service."""
    url = request.form.get("url")
    prompt = request.form.get("prompt", "Hello, how are you?")
    model_id = request.form.get("model_id")
    chat_mode = request.form.get("chat_mode", "false") == "true"

    if not url:
        flash("URL is required", "error")
        return redirect(url_for("index"))

    if chat_mode:
        # Redirect to chat interface
        return render_template("chat_test.html", service_url=url, model_id=model_id)
    else:
        # Regular test mode
        results = await web_app.test_vllm_service(url, prompt, model_id)
        return render_template("test_results.html", results=results)


@app.route("/chat-test")
def chat_test():
    """Chat test interface."""
    url = request.args.get("url")
    model_id = request.args.get("model_id")

    if not url:
        flash("URL is required", "error")
        return redirect(url_for("index"))

    return render_template("chat_test.html", service_url=url, model_id=model_id)


@app.route("/api/ping-service", methods=["POST"])
async def ping_service():
    """Ping a vLLM service to check if it's available."""
    data = request.json
    url = data.get("url")
    model_id = data.get("model_id")

    if not url:
        return jsonify({"success": False, "error": "URL is required"})

    try:
        # Try to ping the service with a simple request
        result = await web_app.test_vllm_service(url, "Hello", model_id, max_tokens=1)
        return jsonify({"success": result["success"]})
    except Exception as e:
        logger.error(f"Error pinging service: {e}")
        return jsonify({"success": False, "error": str(e)})


@app.route("/api/get-models", methods=["POST"])
def get_models():
    """Get available models for a service."""
    data = request.json
    url = data.get("url")

    if not url:
        return jsonify({"success": False, "error": "URL is required"})

    try:
        # Extract host and port from URL
        if url.startswith("http://"):
            url = url[7:]
        elif url.startswith("https://"):
            url = url[8:]

        # Split host and port
        parts = url.split(":")
        host = parts[0]

        # Get services for the host
        services = web_app.get_services(host)

        if services.get("error"):
            return jsonify({"success": False, "error": services["error"]})

        # Find the server with the matching URL
        models = []
        for server in services.get("servers", []):
            server_url = server.get("url", "")
            if url in server_url or server_url in url:
                models = server.get("models", [])
                break

        return jsonify({
            "success": True,
            "models": models
        })
    except Exception as e:
        logger.error(f"Error getting models: {e}")
        return jsonify({"success": False, "error": str(e)})


@app.route("/api/get-all-services")
def get_all_services():
    """Get all available vLLM services across all hosts."""
    try:
        all_services = []

        # Get all hosts
        hosts = web_app.hosts

        # For each host, get services
        for host in hosts:
            services = web_app.get_services(host)
            if not services.get("error") and services.get("servers"):
                for server in services.get("servers", []):
                    service_url = server.get("url", "")
                    if service_url:
                        # Add http:// prefix if not present
                        if not service_url.startswith("http"):
                            service_url = f"http://{service_url}"

                        # Add to list with additional info
                        all_services.append({
                            "url": service_url,
                            "host": host,
                            "port": server.get("port"),
                            "model_count": len(server.get("models", []))
                        })

        return jsonify({
            "success": True,
            "services": all_services
        })
    except Exception as e:
        logger.error(f"Error getting all services: {e}")
        return jsonify({"success": False, "error": str(e)})


@app.route("/api/chat", methods=["POST"])
async def chat():
    """Chat with a vLLM service using streaming."""
    data = request.json
    url = data.get("url")
    model_id = data.get("model_id")
    messages = data.get("messages", [])

    if not url:
        return jsonify({"success": False, "error": "URL is required"})

    if not messages:
        return jsonify({"success": False, "error": "Messages are required"})

    # Create a synchronous generator that wraps the async generator
    def generate():
        # Create a new event loop for this request
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # Run the async generator in the synchronous context
            async def async_gen():
                try:
                    # Stream the response
                    async for chunk in web_app.stream_chat(url, messages, model_id):
                        yield f"{{\"text\": {json.dumps(chunk)}}}\n"
                except Exception as e:
                    logger.error(f"Error in chat streaming: {e}")
                    yield f"{{\"error\": {json.dumps(str(e))}}}\n"

            # Run the async generator and yield each chunk
            async_generator = async_gen()
            while True:
                try:
                    chunk = loop.run_until_complete(async_generator.__anext__())
                    yield chunk
                except StopAsyncIteration:
                    break
        finally:
            loop.close()

    return Response(generate(), mimetype="text/event-stream")


def main():
    """Run the web application."""
    global web_app

    parser = argparse.ArgumentParser(description="Web application to display vLLM services and open ports")
    parser.add_argument("--cache-dir", default=".cache", help="Directory where cache files are stored")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=5000, help="Port to bind to")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")

    args = parser.parse_args()

    # Create web app instance
    web_app = VLLMWebApp(cache_dir=args.cache_dir)

    # Run the web application
    app.run(host=args.host, port=args.port, debug=args.debug)


if __name__ == "__main__":
    main()
