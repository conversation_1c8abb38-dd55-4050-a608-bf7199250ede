#!/usr/bin/env python
"""Example script to detect vLLM models on a server."""

import asyncio
import sys

from vllm_detector.detector import detect_models


async def main():
    """Detect vLLM models on a server."""
    if len(sys.argv) < 2:
        print("Usage: python detect_models.py <server_url>")
        sys.exit(1)
        
    server_url = sys.argv[1]
    print(f"Detecting models on {server_url}...")
    
    try:
        models = await detect_models(server_url)
        
        if not models:
            print("No models detected.")
            return
            
        print(f"Found {len(models)} models:")
        for model in models:
            print(f"- {model.model_id}")
            if model.max_model_len:
                print(f"  Max length: {model.max_model_len}")
            if model.dtype:
                print(f"  Data type: {model.dtype}")
            if model.device_type:
                print(f"  Device: {model.device_type}")
                
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
