{% extends "base.html" %}

{% block title %}vLLM Detector - {{ host }}{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h2>Host: {{ host }}</h2>
    </div>
    <div class="card-body">
        <a href="/" class="btn btn-primary mb-3">Back to Hosts</a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h3>Open Ports</h3>
        {% if open_ports.timestamp_readable %}
            <span class="timestamp">Last updated: {{ open_ports.timestamp_readable }}</span>
        {% endif %}
    </div>
    <div class="card-body">
        {% if open_ports.error %}
            <div class="alert alert-danger">
                {{ open_ports.error }}
            </div>
        {% elif open_ports.open_ports %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Port</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for port in open_ports.open_ports %}
                            <tr>
                                <td>{{ port }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="alert alert-info">
                No open ports found.
            </div>
        {% endif %}
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h3>vLLM Services</h3>
        {% if services.timestamp_readable %}
            <span class="timestamp">Last updated: {{ services.timestamp_readable }}</span>
        {% endif %}
    </div>
    <div class="card-body">
        {% if services.error %}
            <div class="alert alert-danger">
                {{ services.error }}
            </div>
        {% elif services.servers %}
            {% for server in services.servers %}
                <div class="card mb-3">
                    <div class="card-header">
                        <h4>{{ server.url }}</h4>
                    </div>
                    <div class="card-body">
                        <p><strong>Port:</strong> {{ server.port }}</p>
                        {% if server.models %}
                            <h5>Models:</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Model ID</th>
                                            <th>Max Length</th>
                                            <th>Data Type</th>
                                            <th>Device</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for model in server.models %}
                                            <tr>
                                                <td>{{ model.model_id }}</td>
                                                <td>{{ model.max_model_len or 'N/A' }}</td>
                                                <td>{{ model.dtype or 'N/A' }}</td>
                                                <td>{{ model.device_type or 'N/A' }}</td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="alert alert-info">
                                No models found for this server.
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="alert alert-info">
                No vLLM services found.
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
