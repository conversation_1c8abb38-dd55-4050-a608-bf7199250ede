{% extends "base_tailwind.html" %}

{% block title %}vLLM Detector - Chat Test{% endblock %}

{% block head %}
<style>
    .chat-container {
        height: calc(100vh - 300px);
        min-height: 400px;
    }
    .chat-messages {
        height: calc(100% - 80px);
        overflow-y: auto;
    }
    .user-message {
        background-color: #e0f2fe;
        border-radius: 0.75rem 0.75rem 0 0.75rem;
    }
    .assistant-message {
        background-color: #f3f4f6;
        border-radius: 0.75rem 0.75rem 0.75rem 0;
    }
    .typing-indicator {
        display: inline-flex;
        align-items: center;
    }
    .typing-indicator span {
        height: 8px;
        width: 8px;
        margin: 0 1px;
        background-color: #9ca3af;
        border-radius: 50%;
        display: inline-block;
        animation: bounce 1.3s linear infinite;
    }
    .typing-indicator span:nth-child(2) {
        animation-delay: 0.15s;
    }
    .typing-indicator span:nth-child(3) {
        animation-delay: 0.3s;
    }
    .system-message {
        background-color: #fffbeb;
        border: 1px solid #fef3c7;
        border-radius: 0.75rem;
    }
    @keyframes bounce {
        0%, 60%, 100% {
            transform: translateY(0);
        }
        30% {
            transform: translateY(-4px);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="mb-8">
    <div class="card">
        <div class="card-header flex justify-between items-center">
            <h2 class="text-xl font-bold text-gray-800">Chat with vLLM Service</h2>
            <a href="/" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Hosts
            </a>
        </div>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
    <div class="lg:col-span-1">
        <div class="card mb-8">
            <div class="card-header">
                <h3 class="text-lg font-bold text-gray-800">Service Information</h3>
            </div>
            <div class="card-body">
                <div class="space-y-4">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500">Service URL</h4>
                        <div class="space-y-2">
                            <div class="relative">
                                <select id="service-url-select" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500 appearance-none text-sm font-mono">
                                    <option value="{{ service_url }}" selected>{{ service_url }}</option>
                                    <!-- More services will be loaded dynamically -->
                                </select>
                                <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                    <i class="fas fa-chevron-down text-gray-400"></i>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <input type="text" id="service-url-input" placeholder="Or enter custom URL..."
                                       class="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500 font-mono">
                                <button type="button" id="change-url-btn" class="btn btn-primary text-sm py-1 px-2">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500">Model</h4>
                        <div class="relative">
                            <select id="model-selector" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500 appearance-none">
                                <option value="{{ model_id }}" selected>{{ model_id }}</option>
                                <!-- More models will be loaded dynamically -->
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                <i class="fas fa-chevron-down text-gray-400"></i>
                            </div>
                        </div>
                        <div id="model-info" class="mt-2 text-xs text-gray-500 hidden">
                            <div class="grid grid-cols-2 gap-1">
                                <div>Max Length:</div>
                                <div id="model-max-length">-</div>
                                <div>Data Type:</div>
                                <div id="model-dtype">-</div>
                                <div>Device:</div>
                                <div id="model-device">-</div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500">Status</h4>
                        <span id="service-status" class="badge badge-info">Connecting...</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-bold text-gray-800">Preset Prompts</h3>
            </div>
            <div class="card-body">
                <div class="space-y-2">
                    <button type="button" class="w-full text-sm px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-left"
                            onclick="setPrompt('Hello, how are you?')">
                        Basic greeting
                    </button>
                    <button type="button" class="w-full text-sm px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-left"
                            onclick="setPrompt('Explain quantum computing in simple terms.')">
                        Explain quantum computing
                    </button>
                    <button type="button" class="w-full text-sm px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-left"
                            onclick="setPrompt('Write a short poem about artificial intelligence.')">
                        AI poem
                    </button>
                    <button type="button" class="w-full text-sm px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-left"
                            onclick="setPrompt('What are the main differences between Python and JavaScript?')">
                        Python vs JavaScript
                    </button>
                </div>

                <div class="mt-4">
                    <button type="button" class="w-full btn btn-secondary" onclick="clearChat()">
                        <i class="fas fa-trash mr-2"></i>
                        Clear Chat
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="lg:col-span-3">
        <div class="card chat-container">
            <div class="card-header">
                <h3 class="text-lg font-bold text-gray-800">Chat</h3>
            </div>
            <div class="card-body p-0 flex flex-col h-full">
                <div id="chat-messages" class="chat-messages p-4 space-y-4 flex-grow overflow-y-auto">
                    <div class="assistant-message p-3 max-w-3xl">
                        <p>Hello! I'm ready to chat. How can I help you today?</p>
                    </div>
                </div>

                <div class="p-4 border-t border-gray-200">
                    <form id="chat-form" class="flex space-x-2">
                        <input type="text" id="user-input" placeholder="Type your message..."
                               class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                        <button type="submit" id="send-button" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden form for initial data -->
<form id="hidden-data" class="hidden">
    <input type="hidden" id="service-url" value="{{ service_url }}">
    <input type="hidden" id="model-id" value="{{ model_id }}">
</form>
{% endblock %}

{% block scripts %}
<script>
    // Global variables
    let chatHistory = [];
    let isGenerating = false;
    let availableModels = [];
    let currentModelId = '';
    let currentServiceUrl = '';

    // DOM elements
    const chatMessages = document.getElementById('chat-messages');
    const userInput = document.getElementById('user-input');
    const chatForm = document.getElementById('chat-form');
    const sendButton = document.getElementById('send-button');
    const serviceStatus = document.getElementById('service-status');
    const serviceUrlInput = document.getElementById('service-url-input');
    const serviceUrlSelect = document.getElementById('service-url-select');
    const changeUrlBtn = document.getElementById('change-url-btn');
    const modelSelector = document.getElementById('model-selector');
    const initialModelId = document.getElementById('model-id').value;
    const initialServiceUrl = document.getElementById('service-url').value;
    const modelInfo = document.getElementById('model-info');
    const modelMaxLength = document.getElementById('model-max-length');
    const modelDtype = document.getElementById('model-dtype');
    const modelDevice = document.getElementById('model-device');

    // Set initial values
    currentModelId = initialModelId;
    currentServiceUrl = initialServiceUrl;

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        // Focus on input
        userInput.focus();

        // Check service status
        checkServiceStatus();

        // Load available models
        loadAvailableModels();

        // Load all available services
        loadAllServices();

        // Add event listener for model selector
        modelSelector.addEventListener('change', function() {
            currentModelId = this.value;
            // Add a system message to indicate model change
            addMessage('assistant', `Model switched to ${currentModelId}`, true);
            // Clear chat history to avoid confusion with different models
            chatHistory = [];
            // Check service status with new model
            checkServiceStatus();
            // Update model info
            const selectedModel = availableModels.find(model => model.model_id === currentModelId);
            updateModelInfo(selectedModel);
        });

        // Add event listener for service URL selector
        serviceUrlSelect.addEventListener('change', function() {
            const newUrl = this.value;
            if (newUrl === '') {
                // User selected the custom URL option, focus on the input field
                serviceUrlInput.focus();
                return;
            }

            if (newUrl && newUrl !== currentServiceUrl) {
                // Update current URL and input field
                currentServiceUrl = newUrl;
                serviceUrlInput.value = '';
                // Add a system message to indicate URL change
                addMessage('assistant', `Service URL changed to ${currentServiceUrl}`, true);
                // Clear chat history and model selector
                chatHistory = [];
                clearModelSelector();
                // Check service status with new URL
                checkServiceStatus();
                // Load available models for new URL
                loadAvailableModels();
            }
        });

        // Add event listener for URL change button
        changeUrlBtn.addEventListener('click', function() {
            const newUrl = serviceUrlInput.value.trim();
            if (newUrl && newUrl !== currentServiceUrl) {
                // Update current URL
                currentServiceUrl = newUrl;
                // Reset the URL selector to avoid confusion
                serviceUrlSelect.value = '';
                // Add a system message to indicate URL change
                addMessage('assistant', `Service URL changed to ${currentServiceUrl}`, true);
                // Clear chat history and model selector
                chatHistory = [];
                clearModelSelector();
                // Check service status with new URL
                checkServiceStatus();
                // Load available models for new URL
                loadAvailableModels();
            }
        });

        // Add event listener for URL input (Enter key)
        serviceUrlInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                changeUrlBtn.click();
            }
        });
    });

    // Event listeners
    chatForm.addEventListener('submit', function(e) {
        e.preventDefault();
        sendMessage();
    });

    // Functions
    function setPrompt(text) {
        userInput.value = text;
        userInput.focus();
    }

    function clearChat() {
        // Clear chat history
        chatHistory = [];

        // Clear chat messages except the first one
        while (chatMessages.children.length > 1) {
            chatMessages.removeChild(chatMessages.lastChild);
        }
    }

    function checkServiceStatus() {
        // Update status to connecting
        updateServiceStatus('connecting');

        // Send a ping to the service
        fetch('/api/ping-service', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                url: currentServiceUrl,
                model_id: currentModelId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateServiceStatus('connected');
            } else {
                updateServiceStatus('error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            updateServiceStatus('error');
        });
    }

    function loadAvailableModels() {
        // Send request to get available models
        fetch('/api/get-models', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                url: currentServiceUrl
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.models && data.models.length > 0) {
                availableModels = data.models;

                // Clear existing options except the first one (current model)
                while (modelSelector.options.length > 1) {
                    modelSelector.remove(1);
                }

                // Add models to selector
                availableModels.forEach(model => {
                    const modelId = model.model_id;

                    // Skip if it's the current model (already added)
                    if (modelId === currentModelId) {
                        // Update model info for current model
                        updateModelInfo(model);
                        return;
                    }

                    const option = document.createElement('option');
                    option.value = modelId;
                    option.textContent = modelId;
                    modelSelector.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading models:', error);
        });
    }

    function updateModelInfo(model) {
        if (!model) {
            modelInfo.classList.add('hidden');
            return;
        }

        // Show model info section
        modelInfo.classList.remove('hidden');

        // Update model info
        modelMaxLength.textContent = model.max_model_len || 'N/A';
        modelDtype.textContent = model.dtype || 'N/A';
        modelDevice.textContent = model.device_type || 'N/A';
    }

    function clearModelSelector() {
        // Clear all options
        while (modelSelector.options.length > 0) {
            modelSelector.remove(0);
        }

        // Add a placeholder option
        const option = document.createElement('option');
        option.value = '';
        option.textContent = 'Loading models...';
        option.disabled = true;
        option.selected = true;
        modelSelector.appendChild(option);

        // Hide model info
        modelInfo.classList.add('hidden');
    }

    function loadAllServices() {
        // Send request to get all available services
        fetch('/api/get-all-services')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.services && data.services.length > 0) {
                    // Clear existing options except the first one (current service)
                    // Keep the current service as the first option
                    const currentOption = serviceUrlSelect.options[0];
                    while (serviceUrlSelect.options.length > 1) {
                        serviceUrlSelect.remove(1);
                    }

                    // Add services to selector
                    data.services.forEach(service => {
                        const serviceUrl = service.url;

                        // Skip if it's the current service (already added)
                        if (serviceUrl === currentServiceUrl) {
                            return;
                        }

                        const option = document.createElement('option');
                        option.value = serviceUrl;

                        // Create a descriptive label with host, port and model count
                        const modelText = service.model_count === 1 ? 'model' : 'models';
                        option.textContent = `${serviceUrl} (${service.model_count} ${modelText})`;

                        serviceUrlSelect.appendChild(option);
                    });

                    // Add a custom URL option
                    const customOption = document.createElement('option');
                    customOption.value = '';
                    customOption.textContent = '-- Enter custom URL below --';
                    customOption.disabled = true;
                    serviceUrlSelect.appendChild(customOption);
                }
            })
            .catch(error => {
                console.error('Error loading services:', error);
            });
    }

    function updateServiceStatus(status) {
        if (status === 'connecting') {
            serviceStatus.className = 'badge badge-info';
            serviceStatus.textContent = 'Connecting...';
        } else if (status === 'connected') {
            serviceStatus.className = 'badge badge-success';
            serviceStatus.textContent = 'Connected';
        } else if (status === 'error') {
            serviceStatus.className = 'badge badge-danger';
            serviceStatus.textContent = 'Connection Error';
        } else if (status === 'generating') {
            serviceStatus.className = 'badge badge-warning';
            serviceStatus.textContent = 'Generating...';
        }
    }

    function sendMessage() {
        const message = userInput.value.trim();

        if (message === '' || isGenerating) return;

        // Add user message to chat
        addMessage('user', message);

        // Clear input
        userInput.value = '';

        // Add to chat history
        chatHistory.push({role: 'user', content: message});

        // Show typing indicator
        addTypingIndicator();

        // Set generating flag
        isGenerating = true;
        updateServiceStatus('generating');

        // Disable send button
        sendButton.disabled = true;

        // Send message to server
        fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                url: currentServiceUrl,
                model_id: currentModelId,
                messages: chatHistory
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            // Set up streaming
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            // Remove typing indicator
            removeTypingIndicator();

            // Add empty assistant message
            const assistantMessageId = addMessage('assistant', '');

            // Read stream
            function readStream() {
                return reader.read().then(({ done, value }) => {
                    if (done) {
                        // End of stream
                        isGenerating = false;
                        updateServiceStatus('connected');
                        sendButton.disabled = false;

                        // Add to chat history
                        const assistantMessage = document.getElementById(assistantMessageId).querySelector('p').textContent;
                        chatHistory.push({role: 'assistant', content: assistantMessage});

                        return;
                    }

                    // Decode chunk
                    buffer += decoder.decode(value, {stream: true});

                    // Process complete messages
                    let messages = buffer.split('\n');
                    buffer = messages.pop(); // Keep the last incomplete message in the buffer

                    for (const message of messages) {
                        if (message.trim() === '') continue;

                        try {
                            const data = JSON.parse(message);
                            if (data.text) {
                                // Update assistant message
                                updateMessage(assistantMessageId, data.text);
                            }
                        } catch (e) {
                            console.error('Error parsing JSON:', e);
                        }
                    }

                    // Continue reading
                    return readStream();
                });
            }

            return readStream();
        })
        .catch(error => {
            console.error('Error:', error);

            // Remove typing indicator
            removeTypingIndicator();

            // Add error message
            addMessage('assistant', 'Sorry, there was an error processing your request. Please try again.');

            // Reset state
            isGenerating = false;
            updateServiceStatus('error');
            sendButton.disabled = false;
        });
    }

    function addMessage(role, content, isSystem = false) {
        const messageDiv = document.createElement('div');
        const messageId = 'msg-' + Date.now();
        messageDiv.id = messageId;

        // Determine message class based on role and if it's a system message
        if (isSystem) {
            messageDiv.className = 'system-message p-3 max-w-3xl';
        } else {
            messageDiv.className = role === 'user' ? 'user-message p-3 ml-auto max-w-3xl' : 'assistant-message p-3 max-w-3xl';
        }

        const messageContent = document.createElement('p');
        messageContent.textContent = content;
        messageDiv.appendChild(messageContent);

        chatMessages.appendChild(messageDiv);

        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;

        return messageId;
    }

    function updateMessage(messageId, content) {
        const messageDiv = document.getElementById(messageId);
        if (messageDiv) {
            const messageContent = messageDiv.querySelector('p');
            messageContent.textContent = content;

            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
    }

    function addTypingIndicator() {
        const indicatorDiv = document.createElement('div');
        indicatorDiv.id = 'typing-indicator';
        indicatorDiv.className = 'assistant-message p-3 max-w-3xl';

        const indicator = document.createElement('div');
        indicator.className = 'typing-indicator';
        indicator.innerHTML = '<span></span><span></span><span></span>';

        indicatorDiv.appendChild(indicator);
        chatMessages.appendChild(indicatorDiv);

        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    function removeTypingIndicator() {
        const indicator = document.getElementById('typing-indicator');
        if (indicator) {
            chatMessages.removeChild(indicator);
        }
    }
</script>
{% endblock %}
