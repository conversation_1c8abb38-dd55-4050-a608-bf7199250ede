{% extends "base.html" %}

{% block title %}vLLM Detector - Hosts{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h2>Monitored Hosts</h2>
    </div>
    <div class="card-body">
        {% if hosts %}
            <div class="list-group">
                {% for host in hosts %}
                    <a href="/host/{{ host }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">{{ host }}</h5>
                        </div>
                        <p class="mb-1">View open ports and vLLM services</p>
                    </a>
                {% endfor %}
            </div>
        {% else %}
            <div class="alert alert-info">
                No hosts found. Start the scanner service to monitor hosts.
            </div>
        {% endif %}
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h2>Scanner Service</h2>
    </div>
    <div class="card-body">
        <p>The scanner service periodically scans hosts for open ports and vLLM services.</p>
        <p>To start the scanner service, run:</p>
        <pre><code>./examples/start_service.sh --host &lt;host&gt; --port-min &lt;min_port&gt; --port-max &lt;max_port&gt; --concurrency &lt;num_concurrent&gt;</code></pre>
        <p>To stop the scanner service, run:</p>
        <pre><code>./examples/stop_service.sh</code></pre>
    </div>
</div>
{% endblock %}
