{% extends "base_tailwind.html" %}

{% block title %}vLLM Detector - Test Results{% endblock %}

{% block content %}
<div class="mb-8">
    <div class="card">
        <div class="card-header flex justify-between items-center">
            <h2 class="text-xl font-bold text-gray-800">Test Results</h2>
            <a href="/" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Hosts
            </a>
        </div>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <div class="lg:col-span-1">
        <div class="card mb-8">
            <div class="card-header">
                <h3 class="text-lg font-bold text-gray-800">Test Information</h3>
            </div>
            <div class="card-body">
                <div class="space-y-4">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500">Service URL</h4>
                        <p class="text-gray-900 font-mono">{{ results.url }}</p>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500">Status</h4>
                        {% if results.success %}
                            <span class="badge badge-success">Success</span>
                        {% else %}
                            <span class="badge badge-danger">Failed</span>
                        {% endif %}
                    </div>

                    {% if results.endpoint %}
                    <div>
                        <h4 class="text-sm font-medium text-gray-500">Endpoint</h4>
                        <p class="text-gray-900 font-mono">{{ results.endpoint }}</p>
                    </div>
                    {% endif %}

                    {% if results.latency %}
                    <div>
                        <h4 class="text-sm font-medium text-gray-500">Latency</h4>
                        <p class="text-gray-900">{{ results.latency|round(3) }} seconds</p>
                    </div>
                    {% endif %}

                    {% if results.payload %}
                    <div>
                        <h4 class="text-sm font-medium text-gray-500">Request Type</h4>
                        <p class="text-gray-900">
                            {% if 'messages' in results.payload %}
                                Chat Completions API
                            {% elif 'prompt' in results.payload %}
                                Completions API
                            {% else %}
                                Custom API
                            {% endif %}
                        </p>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500">Model</h4>
                        <p class="text-gray-900">
                            {% if results.payload.model %}
                                {{ results.payload.model }}
                            {% else %}
                                <span class="text-gray-500 italic">Not specified</span>
                            {% endif %}
                        </p>
                    </div>
                    {% endif %}

                    {% if results.error %}
                    <div>
                        <h4 class="text-sm font-medium text-gray-500">Error</h4>
                        <p class="text-red-600">{{ results.error }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-bold text-gray-800">Prompt</h3>
            </div>
            <div class="card-body">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-gray-800 whitespace-pre-wrap">{{ results.prompt }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="lg:col-span-2">
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-bold text-gray-800">Response</h3>
            </div>
            <div class="card-body">
                {% if results.success and results.response %}
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <pre class="text-sm text-gray-800 whitespace-pre-wrap overflow-x-auto">{{ results.response|tojson(indent=2) }}</pre>
                    </div>

                    {% if results.response.choices %}
                        <div class="mt-6">
                            <h4 class="text-md font-semibold text-gray-700 mb-2">Generated Text</h4>
                            <div class="bg-white border border-gray-200 p-4 rounded-lg">
                                {% if results.response.choices[0].text %}
                                    <p class="text-gray-800 whitespace-pre-wrap">{{ results.response.choices[0].text }}</p>
                                {% elif results.response.choices[0].message and results.response.choices[0].message.content %}
                                    <p class="text-gray-800 whitespace-pre-wrap">{{ results.response.choices[0].message.content }}</p>
                                {% else %}
                                    <p class="text-gray-500 italic">No text content found in response</p>
                                {% endif %}
                            </div>
                        </div>
                    {% endif %}

                    {% if results.response.generated_text %}
                        <div class="mt-6">
                            <h4 class="text-md font-semibold text-gray-700 mb-2">Generated Text</h4>
                            <div class="bg-white border border-gray-200 p-4 rounded-lg">
                                <p class="text-gray-800 whitespace-pre-wrap">{{ results.response.generated_text }}</p>
                            </div>
                        </div>
                    {% endif %}
                {% else %}
                    <div class="p-4 bg-red-50 text-red-800 rounded-lg">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-500"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium">Failed to get response from the service.</p>
                                {% if results.error %}
                                    <p class="text-sm mt-1">{{ results.error }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
