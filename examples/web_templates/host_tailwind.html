{% extends "base_tailwind.html" %}

{% block title %}vLLM Detector - {{ host }}{% endblock %}

{% block content %}
<div class="mb-8">
    <div class="card">
        <div class="card-header flex justify-between items-center">
            <h2 class="text-xl font-bold text-gray-800">Host: {{ host }}</h2>
            <a href="/" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Hosts
            </a>
        </div>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <div class="lg:col-span-1">
        <div class="card mb-8">
            <div class="card-header">
                <h3 class="text-lg font-bold text-gray-800">Open Ports</h3>
                {% if open_ports.timestamp_readable %}
                    <span class="text-xs text-gray-500">Last updated: {{ open_ports.timestamp_readable }}</span>
                {% endif %}
            </div>
            <div class="card-body">
                {% if open_ports.error %}
                    <div class="p-4 bg-red-50 text-red-800 rounded-lg">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-500"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium">{{ open_ports.error }}</p>
                            </div>
                        </div>
                    </div>
                {% elif open_ports.open_ports %}
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
                        {% for port in open_ports.open_ports %}
                            <div class="bg-gray-50 rounded-lg p-3 text-center">
                                <span class="font-mono text-gray-800">{{ port }}</span>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="p-4 bg-blue-50 text-blue-800 rounded-lg">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle text-blue-500"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium">No open ports found.</p>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="lg:col-span-2">
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-bold text-gray-800">vLLM Services</h3>
                {% if services.timestamp_readable %}
                    <span class="text-xs text-gray-500">Last updated: {{ services.timestamp_readable }}</span>
                {% endif %}
            </div>
            <div class="card-body">
                {% if services.error %}
                    <div class="p-4 bg-red-50 text-red-800 rounded-lg">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-500"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium">{{ services.error }}</p>
                            </div>
                        </div>
                    </div>
                {% elif services.servers %}
                    <div class="space-y-6">
                        {% for server in services.servers %}
                            <div class="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
                                <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                                    <div class="flex justify-between items-center">
                                        <h4 class="text-lg font-semibold text-gray-800">{{ server.url }}</h4>
                                        <span class="badge badge-success">Port {{ server.port }}</span>
                                    </div>
                                </div>
                                <div class="p-4">
                                    <div class="flex justify-between items-center mb-4">
                                        <h5 class="text-md font-semibold text-gray-700">
                                            <i class="fas fa-microchip mr-2"></i>
                                            Models
                                        </h5>
                                        <button type="button" class="btn btn-primary text-sm" onclick="openPromptModal('{{ server.url }}', '{% if server.models and server.models|length > 0 %}{{ server.models[0].model_id }}{% endif %}')">
                                            <i class="fas fa-play mr-1"></i>
                                            Test Service
                                        </button>
                                    </div>

                                    {% if server.models %}
                                        <div class="overflow-x-auto">
                                            <table class="min-w-full divide-y divide-gray-200">
                                                <thead class="bg-gray-50">
                                                    <tr>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Model ID</th>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Max Length</th>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data Type</th>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Device</th>
                                                    </tr>
                                                </thead>
                                                <tbody class="bg-white divide-y divide-gray-200">
                                                    {% for model in server.models %}
                                                        <tr>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ model.model_id }}</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ model.max_model_len or 'N/A' }}</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ model.dtype or 'N/A' }}</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ model.device_type or 'N/A' }}</td>
                                                        </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    {% else %}
                                        <div class="p-4 bg-blue-50 text-blue-800 rounded-lg">
                                            <div class="flex">
                                                <div class="flex-shrink-0">
                                                    <i class="fas fa-info-circle text-blue-500"></i>
                                                </div>
                                                <div class="ml-3">
                                                    <p class="text-sm font-medium">No models found for this server.</p>
                                                </div>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="p-4 bg-blue-50 text-blue-800 rounded-lg">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle text-blue-500"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium">No vLLM services found.</p>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Prompt Modal -->
<div id="promptModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-800">Test vLLM Service</h3>
            <button type="button" class="text-gray-400 hover:text-gray-500" onclick="closePromptModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form id="testServiceForm" action="/test-service" method="post">
            <div class="px-6 py-4 space-y-4">
                <input type="hidden" id="modalUrl" name="url" value="">
                <input type="hidden" id="modalModelId" name="model_id" value="">

                <div>
                    <label for="modalPrompt" class="block text-sm font-medium text-gray-700 mb-1">Prompt</label>
                    <textarea id="modalPrompt" name="prompt" rows="5"
                              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">Hello, how are you?</textarea>
                    <p class="mt-1 text-xs text-gray-500">Enter your prompt for the model</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Preset Prompts</label>
                    <div class="grid grid-cols-2 gap-2 mb-2">
                        <button type="button" class="text-sm px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-lg text-left"
                                onclick="setPrompt('Hello, how are you?')">
                            Basic greeting
                        </button>
                        <button type="button" class="text-sm px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-lg text-left"
                                onclick="setPrompt('Explain quantum computing in simple terms.')">
                            Explain quantum computing
                        </button>
                        <button type="button" class="text-sm px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-lg text-left"
                                onclick="setPrompt('Write a short poem about artificial intelligence.')">
                            AI poem
                        </button>
                        <button type="button" class="text-sm px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-lg text-left"
                                onclick="setPrompt('What are the main differences between Python and JavaScript?')">
                            Python vs JavaScript
                        </button>
                    </div>

                    <div class="flex space-x-2">
                        <input type="text" id="customPrompt" placeholder="Enter custom prompt"
                               class="flex-1 px-3 py-1 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                        <button type="button" class="btn btn-secondary text-sm py-1"
                                onclick="setPrompt(document.getElementById('customPrompt').value)">
                            Add
                        </button>
                    </div>
                </div>
            </div>
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-2">
                <button type="button" class="btn btn-secondary" onclick="closePromptModal()">
                    Cancel
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-play mr-2"></i>
                    Test
                </button>
                <button type="submit" name="chat_mode" value="true" class="btn btn-secondary">
                    <i class="fas fa-comments mr-2"></i>
                    Chat Mode
                </button>
            </div>
        </form>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    function openPromptModal(url, modelId) {
        document.getElementById('modalUrl').value = url;
        document.getElementById('modalModelId').value = modelId;
        document.getElementById('promptModal').classList.remove('hidden');
    }

    function closePromptModal() {
        document.getElementById('promptModal').classList.add('hidden');
    }

    function setPrompt(promptText) {
        document.getElementById('modalPrompt').value = promptText;
    }
</script>
{% endblock %}
