{% extends "base_tailwind.html" %}

{% block title %}vLLM Detector - Hosts{% endblock %}

{% block content %}
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <div class="lg:col-span-2">
        <div class="card mb-8">
            <div class="card-header">
                <h2 class="text-xl font-bold text-gray-800">Monitored Hosts</h2>
            </div>
            <div class="card-body">
                {% if hosts %}
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {% for host in hosts %}
                            <a href="/host/{{ host }}" class="block p-6 bg-white border border-gray-200 rounded-lg shadow hover:bg-gray-50 transition-colors duration-200">
                                <div class="flex items-center justify-between mb-2">
                                    <h5 class="text-lg font-bold text-gray-900">{{ host }}</h5>
                                    <span class="badge badge-info">Host</span>
                                </div>
                                <p class="text-gray-600">View open ports and vLLM services</p>
                                <div class="mt-4 flex justify-end">
                                    <span class="text-primary-600 hover:text-primary-700">
                                        <i class="fas fa-arrow-right"></i>
                                    </span>
                                </div>
                            </a>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="p-4 bg-blue-50 text-blue-800 rounded-lg">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle text-blue-500"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium">
                                    No hosts found. Start the scanner service to monitor hosts.
                                </p>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="lg:col-span-1">
        <div class="card mb-8">
            <div class="card-header">
                <h2 class="text-xl font-bold text-gray-800">Test vLLM Service</h2>
            </div>
            <div class="card-body">
                <form action="/test-service" method="post" class="space-y-4">
                    <div>
                        <label for="url" class="block text-sm font-medium text-gray-700 mb-1">Service URL</label>
                        <input type="text" name="url" id="url" placeholder="http://example.com:8000"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                        <p class="mt-1 text-xs text-gray-500">Enter the URL of the vLLM service to test</p>
                    </div>
                    <div>
                        <label for="model_id" class="block text-sm font-medium text-gray-700 mb-1">Model ID</label>
                        <input type="text" name="model_id" id="model_id" placeholder="model_name"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                        <p class="mt-1 text-xs text-gray-500">Enter the model ID (optional, will use first available model if not specified)</p>
                    </div>
                    <div>
                        <label for="prompt" class="block text-sm font-medium text-gray-700 mb-1">Prompt</label>
                        <textarea name="prompt" id="prompt" rows="3"
                                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">Hello, how are you?</textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Preset Prompts</label>
                        <div class="grid grid-cols-2 gap-2 mb-2">
                            <button type="button" class="text-sm px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-lg text-left"
                                    onclick="document.getElementById('prompt').value = 'Hello, how are you?'">
                                Basic greeting
                            </button>
                            <button type="button" class="text-sm px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-lg text-left"
                                    onclick="document.getElementById('prompt').value = 'Explain quantum computing in simple terms.'">
                                Explain quantum computing
                            </button>
                            <button type="button" class="text-sm px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-lg text-left"
                                    onclick="document.getElementById('prompt').value = 'Write a short poem about artificial intelligence.'">
                                AI poem
                            </button>
                            <button type="button" class="text-sm px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-lg text-left"
                                    onclick="document.getElementById('prompt').value = 'What are the main differences between Python and JavaScript?'">
                                Python vs JavaScript
                            </button>
                        </div>

                        <div class="flex space-x-2">
                            <input type="text" id="customPromptIndex" placeholder="Enter custom prompt"
                                   class="flex-1 px-3 py-1 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                            <button type="button" class="btn btn-secondary text-sm py-1"
                                    onclick="document.getElementById('prompt').value = document.getElementById('customPromptIndex').value">
                                Add
                            </button>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button type="submit" class="btn btn-primary flex-1">
                            <i class="fas fa-play mr-2"></i>
                            Test Service
                        </button>
                        <button type="submit" name="chat_mode" value="true" class="btn btn-secondary flex-1">
                            <i class="fas fa-comments mr-2"></i>
                            Chat Mode
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2 class="text-xl font-bold text-gray-800">Scanner Service</h2>
            </div>
            <div class="card-body">
                <p class="text-gray-600 mb-4">The scanner service periodically scans hosts for open ports and vLLM services.</p>

                <div class="bg-gray-50 p-4 rounded-lg mb-4">
                    <h3 class="text-sm font-semibold text-gray-700 mb-2">Start the scanner service:</h3>
                    <div class="bg-gray-800 text-gray-200 p-3 rounded-lg text-sm font-mono overflow-x-auto">
                        ./examples/start_service.sh --host &lt;host&gt; --port-min &lt;min_port&gt; --port-max &lt;max_port&gt; --concurrency &lt;num_concurrent&gt;
                    </div>
                </div>

                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-sm font-semibold text-gray-700 mb-2">Stop the scanner service:</h3>
                    <div class="bg-gray-800 text-gray-200 p-3 rounded-lg text-sm font-mono">
                        ./examples/stop_service.sh
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
