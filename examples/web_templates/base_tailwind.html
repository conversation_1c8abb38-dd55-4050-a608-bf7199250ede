<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}vLLM Detector{% endblock %}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                    }
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer components {
            .btn {
                @apply px-4 py-2 rounded-lg font-medium transition-colors duration-200;
            }
            .btn-primary {
                @apply bg-primary-600 text-white hover:bg-primary-700;
            }
            .btn-secondary {
                @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
            }
            .btn-danger {
                @apply bg-red-600 text-white hover:bg-red-700;
            }
            .card {
                @apply bg-white rounded-lg shadow-md overflow-hidden;
            }
            .card-header {
                @apply px-6 py-4 bg-gray-50 border-b border-gray-200;
            }
            .card-body {
                @apply p-6;
            }
            .badge {
                @apply px-2 py-1 text-xs font-semibold rounded-full;
            }
            .badge-success {
                @apply bg-green-100 text-green-800;
            }
            .badge-warning {
                @apply bg-yellow-100 text-yellow-800;
            }
            .badge-danger {
                @apply bg-red-100 text-red-800;
            }
            .badge-info {
                @apply bg-blue-100 text-blue-800;
            }
        }
    </style>
    {% block head %}{% endblock %}
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <nav class="bg-white shadow-md rounded-lg mb-8">
            <div class="container mx-auto px-6 py-3">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <a href="/" class="text-2xl font-bold text-primary-600 flex items-center">
                            <i class="fas fa-robot mr-2"></i>
                            <span>vLLM Detector</span>
                        </a>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="/" class="text-gray-700 hover:text-primary-600">
                            <i class="fas fa-home mr-1"></i>
                            <span>Hosts</span>
                        </a>
                        <a href="https://github.com/yourusername/vllm-detector" target="_blank" class="text-gray-700 hover:text-primary-600">
                            <i class="fab fa-github mr-1"></i>
                            <span>GitHub</span>
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="mb-8">
                    {% for category, message in messages %}
                        <div class="p-4 rounded-lg {% if category == 'error' %}bg-red-100 text-red-800{% elif category == 'success' %}bg-green-100 text-green-800{% else %}bg-blue-100 text-blue-800{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <div class="mb-8">
            {% block content %}{% endblock %}
        </div>

        <footer class="mt-12 text-center text-gray-500 text-sm">
            <p>&copy; 2025 vLLM Detector. All rights reserved.</p>
        </footer>
    </div>

    {% block scripts %}{% endblock %}
</body>
</html>
