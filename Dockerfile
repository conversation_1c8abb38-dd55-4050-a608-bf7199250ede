FROM python:3.10-slim

WORKDIR /app

# Set up USTC mirrors for faster builds in China (commented out due to file not found)
# RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list && \
#     sed -i 's/security.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    nmap \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY requirements.txt .

# Install Python dependencies with USTC PyPI mirror
RUN pip install --no-cache-dir -i https://mirrors.ustc.edu.cn/pypi/web/simple --trusted-host mirrors.ustc.edu.cn -r requirements.txt

# Copy application code
COPY . .

# Install the vllm-detector package in development mode
RUN pip install -e .

# Create cache directory
RUN mkdir -p .cache && chmod 777 .cache

# Expose port
EXPOSE 5000

# Set environment variables
ENV PYTHONUNBUFFERED=1

# Copy entrypoint script
COPY docker-entrypoint.sh /app/docker-entrypoint.sh
RUN chmod +x /app/docker-entrypoint.sh

# Run the entrypoint script
ENTRYPOINT ["/app/docker-entrypoint.sh"]
