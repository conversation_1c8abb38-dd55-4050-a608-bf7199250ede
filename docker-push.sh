#!/bin/bash

# Default values
IMAGE_NAME="vllm-detector"
IMAGE_TAG="latest"
REGISTRY=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --tag)
      IMAGE_TAG="$2"
      shift 2
      ;;
    --registry)
      REGISTRY="$2"
      shift 2
      ;;
    --name)
      IMAGE_NAME="$2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: $0 [--tag TAG] [--registry REGISTRY] [--name IMAGE_NAME]"
      exit 1
      ;;
  esac
done

# Set full image name
if [ -n "$REGISTRY" ]; then
  FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
else
  FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
fi

echo "Pushing Docker image: $FULL_IMAGE_NAME"

# Push the Docker image to the registry
docker push $FULL_IMAGE_NAME

echo "Docker image pushed successfully: $FULL_IMAGE_NAME"
